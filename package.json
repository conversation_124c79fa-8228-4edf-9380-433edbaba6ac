{"name": "dna_management_system", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.2.8", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^27.5.2", "@types/node": "^16.18.113", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/yup": "^0.32.0", "@yudiel/react-qr-scanner": "^2.0.8", "antd": "^5.21.2", "axios": "^1.7.7", "bootstrap": "^5.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "jquery": "^3.7.1", "moment": "^2.30.1", "pdfjs-dist": "^5.3.31", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-loading": "^2.0.3", "react-paginate": "^8.2.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "redux-persist": "^6.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}