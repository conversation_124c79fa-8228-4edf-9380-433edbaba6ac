import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  DashboardSummary,
  ManageDashboardState,
  SuccessMessageInterface,
} from "../../../types/redux.type";
import { AppDispatch } from "../../store";
import flashMessage from "../../../utils/notifications/antdMessageUtils";
import { dasboardManagementServices } from "../../../services/endpoints/dashboard-management.api";

// Initial state
const initialState: ManageDashboardState = {

  records: {
    total_patients: 0,
    total_research: 0,
    total_signed_consent: 0,
  },
};

// Slice
const manageDashboardSlice = createSlice({
  name: "manageDashboard",
  initialState,
  reducers: {
    
    setRecords: (state, action: PayloadAction<DashboardSummary>) => {
      state.records = action.payload;
    },
    resetState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

// Actions
export const {

  setRecords,
  resetState,
} = manageDashboardSlice.actions;

// Helper functions
const handleApiError = (error: unknown, dispatch: AppDispatch) => {
  console.error("API Error:", error);
  flashMessage("Internal Server Error", "error");
  dispatch(resetState());
};

const handleApiResponse = (
  response: SuccessMessageInterface,
  dispatch: AppDispatch,
  callback?: (res: SuccessMessageInterface) => void
) => {
  if (response.success) {
    flashMessage(response.message, "success");
  } else {
    flashMessage(response.message, "error");
  }

  if (callback) {
    callback(response);
  }
};




  export const getAdminDashboardData =
  () => async (dispatch: AppDispatch) => {
    try {
      const response = await dasboardManagementServices.getAdminDashboardData();

      if (!response.success) {
        dispatch(resetState());
        flashMessage(response.message, "error");
        return;
      }

      const responseData = response.data;
      if (!responseData) {
        dispatch(resetState());
        return;
      }
    
      // Update state with response data
      dispatch(setRecords(responseData ?? []));
    
  
    } catch (error) {
      handleApiError(error, dispatch);
    }
  };

 



export default manageDashboardSlice.reducer;
