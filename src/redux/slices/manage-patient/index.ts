import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  AdminUserRecord,
  KeyPairInterface,
  ManagePatientState,
  SortOrder,
  StatusType,
  SuccessMessageInterface,
} from "../../../types/redux.type";
import { AppDispatch } from "../../store";
import flashMessage from "../../../utils/notifications/antdMessageUtils";
import { patientManagementServices } from "../../../services/endpoints/patient-management.api";

// Initial state
const initialState: ManagePatientState = {
  page: 1,
  perPage: 10,
  sort: "desc",
  sortColumn: "created_at",
  searchText: "",
  status: "all",
  totalRecords: 0,
  totalPages: 1,
  records: [],
};

// Slice
const managePatientSlice = createSlice({
  name: "managePatient",
  initialState,
  reducers: {
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setPerPage: (state, action: PayloadAction<number>) => {
      state.perPage = action.payload;
    },
    setSort: (state, action: PayloadAction<SortOrder>) => {
      state.sort = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.sortColumn = action.payload;
    },
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
    },
    setStatus: (state, action: PayloadAction<StatusType>) => {
      state.status = action.payload;
    },
    setTotalRecords: (state, action: PayloadAction<number>) => {
      state.totalRecords = action.payload;
    },
    setTotalPages: (state, action: PayloadAction<number>) => {
      state.totalPages = action.payload;
    },
    setRecords: (state, action: PayloadAction<AdminUserRecord[]>) => {
      state.records = action.payload;
    },
    resetState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

// Actions
export const {
  setPage,
  setPerPage,
  setSort,
  setSortColumn,
  setSearchText,
  setStatus,
  setTotalRecords,
  setTotalPages,
  setRecords,
  resetState,
} = managePatientSlice.actions;

// Helper functions
const handleApiError = (error: unknown, dispatch: AppDispatch) => {
  console.error("API Error:", error);
  flashMessage("Internal Server Error", "error");
  dispatch(resetState());
};

const handleApiResponse = (
  response: SuccessMessageInterface,
  dispatch: AppDispatch,
  callback?: (res: SuccessMessageInterface) => void
) => {
  if (response.success) {
    flashMessage(response.message, "success");
  } else {
    flashMessage(response.message, "error");
  }

  if (callback) {
    callback(response);
  }
};



export const createAdminPatient =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    try {
      const response = await patientManagementServices.createAdminPatient(data);
      handleApiResponse(response, dispatch, callback);
    } catch (error) {
      handleApiError(error, dispatch);
    }
  };
  export const getAdminPatientList =
  (data: KeyPairInterface) => async (dispatch: AppDispatch) => {
    try {
      const response = await patientManagementServices.fetchAdminPatientList(data);

      if (!response.success) {
        dispatch(resetState());
        flashMessage(response.message, "error");
        return;
      }

      const responseData = response.data;
      if (!responseData) {
        dispatch(resetState());
        return;
      }

      // Update state with response data
      dispatch(setRecords(responseData.records ?? []));
      dispatch(setTotalPages(responseData.total_pages ?? 1));
      dispatch(setTotalRecords(responseData.total_records ?? 0));
      dispatch(setStatus(responseData.status ?? "all"));
      dispatch(setSearchText(responseData.search_text ?? ""));
      dispatch(setSortColumn(responseData.sort_column ?? "created_at"));
      dispatch(setSort(responseData.sort ?? "desc"));
      dispatch(setPerPage(responseData.per_page ?? 10));
      dispatch(setPage(responseData.page ?? 1));
  
    } catch (error) {
      handleApiError(error, dispatch);
    }
  };

  export const updateAdminPatientStatus =
  (
    data: KeyPairInterface,
    userId: string,
    callback?: (res: SuccessMessageInterface) => void
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      const response = await patientManagementServices.updatePatientUserStatus(
        data,
        userId
      );
      handleApiResponse(response, dispatch, callback);
    } catch (error) {
      handleApiError(error, dispatch);
    }
  };



export default managePatientSlice.reducer;
