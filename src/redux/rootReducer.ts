import { combineReducers } from "@reduxjs/toolkit";
import loaderReducer from "./slices/loader";
import authReducer from "./slices/auth";
import themeReducer from "./slices/theme";
import modalReducer from "./slices/modal";
import manageResearchReducer from "./slices/manage-research";
import managePatientReducer from "./slices/manage-patient";
import manageVideoReducer from "./slices/manage-videos";
import manageDashboardReducer from "./slices/manage-dashboard"
import manageDeclarationReducer from "./slices/manage-declarations"
import manageEconsentReducer from "./slices/manage-econsent"
import managePatientConsentReducer from "./slices/manage-patient-consent"
import { GLOBAL_ACTIONS } from "../types/redux.type";
import ManagePatient from "../pages/manage-patient";
import ManageVideos from "../pages/manage-videos"
import ManageDeclarations from "../pages/manage-declarations";


const appReducer = combineReducers({
  loader: loaderReducer,
  auth: authReducer,
  theme: themeReducer,
  modal: modalReducer,
  manageResearch: manageResearchReducer,
  ManagePatient: managePatientReducer,
  managePatientConsent: managePatientConsentReducer,
  ManageVideos:manageVideoReducer,
  ManageDashboard:manageDashboardReducer,
  ManageDeclarations: manageDeclarationReducer,
  ManageEconsent: manageEconsentReducer,

});

type RootState = ReturnType<typeof appReducer>;

const rootReducer = (state: RootState | undefined, action: any): RootState => {
  switch (action.type) {
    case GLOBAL_ACTIONS.RESET_STATE:
      return appReducer({ theme: state?.theme } as RootState, action);

    default:
      return appReducer(state, action);
  }
};

export default rootReducer;
