import { GLOBAL_ACTIONS } from "../types/redux.type";

export const resetState = () => ({
  type: GLOBAL_ACTIONS.RESET_STATE,
});

export const resetFilter = () => ({
  type: GLOBAL_ACTIONS.RESET_FILTER,
});

export { setLoader } from "./slices/loader";
export { openModal, closeModal, setSelectedData } from "./slices/modal";

export {
  loginUser,
  forgetPassword,
  setAuthUser,
  resetPassword,
  updateUserDetails,
  getUserDetails,
  logoutUser,
} from "./slices/auth";
export {
  
  createAdminPatient,
  getAdminPatientList,
  updateAdminPatientStatus,
} from "./slices/manage-patient";

export {
  createVideo,
  updateVideo,
  getVideosList,
  deleteVideo
} from "./slices/manage-videos";
export {
  getAdminDashboardData,
  
} from "./slices/manage-dashboard";
export {
  createDeclaration,
  getAllDeclarationtList,
  deleteDeclaration,
  updateDeclarationDetails
  
} from "./slices/manage-declarations";
export {
  getResearchCategoriesList,
  getAllResearchList,
  addResearchCategory,
  createNewResearch,
  deleteResearch,
  updateResearchStatus,
  viewResearchDocument,
  updateResearchData
  
 
  
} from "./slices/manage-research";
export {
  getEconsentDropDowndData,
  createAdminConsentForm,
  getAllConsentFormsList,
  deleteEconsentForm,
  updateAdminEconsentFormStatus,
  updateAdminConsentForm
} from "./slices/manage-econsent";
export {
  getAdminPatientConsentList
} from "./slices/manage-patient-consent"
export { setTheme } from "./slices/theme";