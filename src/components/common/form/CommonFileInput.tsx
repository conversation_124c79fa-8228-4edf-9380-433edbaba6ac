import React from "react";
import { Col } from "react-bootstrap";
import { CommonInputProps } from "../../../types/formInput.type";

const CommonFileInput: React.FC<CommonInputProps> = ({
  field,
  value,
  onChange,
  onBlur,
  onKeyDown,
  error,
}) => {
  const isRequired = field.required ?? true;
  const colAttributes = field.colProps || { xs: 12 };

  return (
    <Col {...colAttributes}>
      <label htmlFor={field.name} className="form-label required-field">
        {field.label}
        {isRequired && <span className="text-danger ms-1">*</span>}
      </label>
      <input
        id={field.name}
        name={field.name}
        type="file"
        accept={field.accept}
        onChange={onChange}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        required={isRequired}
        className={`form-control ${error ? "is-invalid" : ""}`}
      />
      {error && <div className="text-danger error-text mt-1">{error}</div>}
    </Col>
  );
};

export default CommonFileInput;
