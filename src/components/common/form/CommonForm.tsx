import React from "react";
import CommonInput from "./CommonInput";
import { Form, Row } from "react-bootstrap";
import { CommonFormPropsInterface } from "../../../types/formInput.type";
import {
  handleFormChange,
  handleFormBlur,
  handleFormKeyDown,
} from "./CommonFormEvents";
import CommonSelect from "./CommonSelect";
import CommonTextarea from "./CommonTextarea";
import CommonRadio from "./CommonRadio";
import CommonFileInput from "./CommonFileInput";

const CommonForm: React.FC<CommonFormPropsInterface> = ({
  state,
  setState,
  errorState,
  setErrorState,
  fields,
  onSubmit,
}) => {
  const renderInput = (field: any) => {
    switch (field.type) {
      case "radio":
        return (
          <CommonRadio
            key={field.name}
            field={field}
            options={field.options}
            defaultValue={field.defaultValue}
            value={state[field.name] || ""}
            onChange={(e) =>
              handleFormChange(e, field, setState, setErrorState, state)
            }
            onBlur={(e) => handleFormBlur(e, field, setErrorState, state)}
            onKeyDown={(e) => handleFormKeyDown(e, field, onSubmit)}
            error={errorState[field.name]}
          />
        );

      case "select":
        return (
          <CommonSelect
            key={field.name}
            field={field}
            value={state[field.name] || ""}
            onChange={(e) =>
              handleFormChange(e, field, setState, setErrorState, state)
            }
            onBlur={(e) => handleFormBlur(e, field, setErrorState, state)}
            onKeyDown={(e) => handleFormKeyDown(e, field, onSubmit)}
            error={errorState[field.name]}
          />
        );
      case "textarea":
        return (
          <CommonTextarea
            key={field.name}
            field={field}
            value={state[field.name] || ""}
            onChange={(e) =>
              handleFormChange(e, field, setState, setErrorState, state)
            }
            onBlur={(e) => handleFormBlur(e, field, setErrorState, state)}
            onKeyDown={(e) => handleFormKeyDown(e, field, onSubmit)}
            error={errorState[field.name]}
          />
        );
        case "file":
          // New case for file input
          return (
            <CommonFileInput
              key={field.name}
              field={field}
              value={state[field.name] || null}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Extract the File object correctly here
                const file = e.target.files && e.target.files.length > 0 ? e.target.files[0] : null;
                // Update the form state with the File object
                setState((prev: any) => ({
                  ...prev,
                  [field.name]: file,
                }));
                // Optionally, run validation here or clear errors
                // e.g. setErrorState(...)
              }}
              onBlur={(e) => handleFormBlur(e, field, setErrorState, state)}
              onKeyDown={(e) => handleFormKeyDown(e, field, onSubmit)}
              error={errorState[field.name]}
              accept={field.accept} // pass accept attribute if needed
            />
          );
      default:
        return (
          <CommonInput
            key={field.name}
            field={field}
            value={state[field.name] || ""}
            onChange={(e) =>
              handleFormChange(e, field, setState, setErrorState, state)
            }
            onBlur={(e) => handleFormBlur(e, field, setErrorState, state)}
            onKeyDown={(e) => handleFormKeyDown(e, field, onSubmit)}
            error={errorState[field.name]}
          />
        );
    }
  };

  return (
    <Form  encType="multipart/form-data">
      <Row className="row-gap-3">{fields.map((field) => renderInput(field))}</Row>
    </Form>
  );
};

export default CommonForm;
