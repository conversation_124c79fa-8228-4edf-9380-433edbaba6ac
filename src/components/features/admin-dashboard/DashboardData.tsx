import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";

//import ThemeImage from "../../common/icons/ThemeImage";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowDown, faArrowUp } from "@fortawesome/free-solid-svg-icons";
import { useLocation, useNavigate } from "react-router-dom";
import {
  
  handleFetchAdminDashboardData,
  
  } from "../../../pages/manage-dashboard/manage-dashboard.event";
import { MANAGE_PATIENT_APP_URL,MANAGE_RESEARCH_APP_URL,MANAGE_PATIENT_CONSENT_APP_URL } from "../../../constants/appUrl";

const DashboardData: React.FC  = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const {
      records,
  } = useAppSelector((state: RootState) => state.ManageDashboard);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  useEffect(() => {
    handleFetchAdminDashboardData(dispatch, navigate, location);
  }, []);

  const handleNavigate = (value: string) => {
    let pathName = "";
    let search = "";
  
    if (value === "patient") {
      pathName = MANAGE_PATIENT_APP_URL;
      search = "";
    } else if (value === "research") {
      pathName = MANAGE_RESEARCH_APP_URL;
      search = "";
    } else if (value === "signed_consent") {
      pathName = MANAGE_PATIENT_CONSENT_APP_URL;
      search = `?currentPage=1&sort=desc&sortColumn=assigned_at&searchText=&status=approved&perPage=10`;
    }
  
    navigate(
      {
        pathname: pathName,
        search: search,
      },
      { replace: false }
    );
  };
  
  return (
    <div className="col-lg-12">
      
      
      
      <div className="card-box mb-3">
          <div className="d-flex flex-column numberBox" onClick={() => handleNavigate("patient")}>
            <h4 className="mb-0" >{records.total_patients.toLocaleString()}{" "}
               {/* <span><FontAwesomeIcon icon={faArrowUp}/> 12%</span> */}
            </h4>
            <p className="mb-0">Number of Patients</p>
          </div>
          
           <div className="d-flex flex-column numberBox"  onClick={() => handleNavigate("research")}>
            <h4 className="mb-0" >{records.total_research.toLocaleString()}{" "} 
              {/* <span><FontAwesomeIcon icon={faArrowUp}/> 12%</span> */}
            </h4>
            <p className="mb-0">Number of Research</p>
          </div>

           <div className="d-flex flex-column numberBox"  onClick={() => handleNavigate("signed_consent")}>
            <h4 className="mb-0" >{records.total_signed_consent.toLocaleString()}{" "}
              {/* <span><FontAwesomeIcon icon={faArrowUp}/> 12%</span> */}
            </h4>
            <p className="mb-0">Number of Signed Consent</p>
          </div>

           <div className="d-flex flex-column numberBox">
            <h4 className="mb-0">1,00,218 <span className="yellow"><FontAwesomeIcon icon={faArrowDown}/> 12%</span></h4>
            <p className="mb-0">Total Number of Cases</p>
          </div>
      </div> 

    </div>
  );
};

export default DashboardData;
