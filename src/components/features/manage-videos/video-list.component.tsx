import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Column } from "../../../types/table.type";
import moment from "moment";
import VideoForm from "./VideoForm";
import VideoView from "./video-view-modal.component";

import {
  ISVideoFormField,
  IFetchVideoDetailsParams,
  IVideoRecord
  } from "../../../types/manage-video.type";
 import {
   handleVideoCreation,
   handleVideoUpdate,
   handleFetchAllVideosDetails,
   handleVideoDeletion,
   } from "../../../pages/manage-videos/manage-video.event";
import { Tooltip } from "antd";
import ThemeImage from "../../common/icons/ThemeImage";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle,faAdd, faUser ,faVideo ,faFileText } from "@fortawesome/free-solid-svg-icons";
import CommonButton from "../../common/button/CommonButton";
import {openModal, closeModal } from "../../../redux/action";
import CommonModal from "../../common/Modal/CommonModal";
import CommonDropdown from "../../common/dropdown/CommonDropdown";
import CommonTable from "../../common/table/CommonTable";
import ConfirmationModal from "../../common/Modal/ConfirmationModal";



const VideosList: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    records,
    page,
    totalPages,
    sort,
    sortColumn,
    searchText,
    perPage,
    totalRecords,
    status,
  } = useAppSelector((state: RootState) => state.ManageVideos);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  const [localStatus, setLocalStatus] = useState(status);
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const [localPerPage, setLocalPerPage] = useState(perPage);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

 
  const { isOpen, modalType, selectedData } = useAppSelector(
    (state: RootState) => state.modal
  );
  const navigate = useNavigate();

  useEffect(() => {
    
    const searchParams = new URLSearchParams(location.search);

    setLocalSearchText(searchParams.get("searchText") || "");
   
    setLocalPerPage(Number(searchParams.get("perPage")) || 10);

    const initialParams: IFetchVideoDetailsParams = {
      currentPage: Number(searchParams.get("currentPage")) || 1,
      perPage: Number(searchParams.get("perPage")) || 10,
      sort: searchParams.get("sort") || "desc",
      sortColumn: searchParams.get("sortColumn") || "created_at",
      searchText: searchParams.get("searchText") || "",
     // status: searchParams.get("status") || "all",
    };

    handleFetchAllVideosDetails(dispatch, initialParams, navigate, location);

    // eslint-disable-next-line
  }, [location.search]);
  
  


  const columns: Column[] = [
    { key: "title", label: "Title", sortable: true },
    {
      key: "file_path",
      label: "Video",
      sortable: true
    },
    { key: "created_at", label: "Created On", sortable: true },
    
  ];
 
  const formattedRecords =
    records && records.length > 0
      ? records.map((videos: any) => ({
          ...videos,
          created_at: moment(videos.created_at).format("YYYY-MM-DD"),
         
        }))
      : [];

  const renderActions = (data: any) => (
    <div className="actions_wrap">
     
      <Tooltip title={`View Video Details`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "view", data }))}
        >
          <ThemeImage imageName="viewImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Edit Video Details`}>
        <button
          className="action_btn"
          onClick={() => handleModalOpen("edit", data)}
        >
          <ThemeImage imageName="editImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Delete Video`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "delete", data }))}
        >
          <ThemeImage imageName="deleteImage" />
        </button>
      </Tooltip>
    </div>
  );

  const handleSearchInputChange = (value: string) => {
    setLocalSearchText(value);

    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300); // Adjust the delay as needed (300 ms in this case)

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
    handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: value,
       // status,
        perPage,
      },
      navigate,
      location
    );
  };


  const handlePerPageFilter = (value: number) => {
    setLocalPerPage(Number(value));

    handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
       // status: localStatus,
        perPage: Number(value),
      },
      navigate,
      location
    );
  };

  const handleSort = (column: string) => {
    const newDirection = sort === "asc" ? "desc" : "asc";

    handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: page,
        sort: newDirection,
        sortColumn: column,
        searchText,
       // status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePageChange = (selectedItem: { selected: number }) => {
    handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: selectedItem.selected + 1,
        sort,
        sortColumn,
        searchText,
       // status,
        perPage,
      },
      navigate,
      location
    );
  };
  const [formState, setFormState] = useState<Record<string, string>>({
    title:"",
    description: "",
    video: "",
  });

  const [errorState, setErrorState] = useState<Record<string, string>>({
    title:"",
    description: "",
    video: "",
  });
  const handleModalOpen = (type: string, data: IVideoRecord | null) => {
    if (type === "add") {
      setFormState({
        title:"",
        description: "",
        video: "",
      });
      dispatch(openModal({ type: "add" }));
    } else {
      setFormState({
    
        title: data?.title ? data?.title : "",
        description: data?.description ? data?.description : "",
        file_path: data?.file_path ? data?.file_path : "",
        mimeType: data?.mime_type ? data?.mime_type : "",
      });
     dispatch(openModal({ type: "edit", data }));
    }
  };
  const videoFormFields: ISVideoFormField[] = [
    {
      name: "title",
      label: "Title",
      type: "text",
      placeholder: "Enter Video Title",
      required: true,
      favImage: faFileText,
      maxLength: 50,
      validationRules: {
        type: "titleWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "video",
      label: "Upload video",
      type: "file",
      placeholder: "Upload Video",
      required: true,
      favImage: faVideo,
     // maxLength: 50,
      accept: "video/mp4",
      validationRules: {
        type: "",
        maxSize: 104857600, 
        required: false,
        accept: "video/mp4",
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "description",
      label: "Video Description",
      type: "textarea",
      placeholder: "Video Description",
      required: false,
      favImage: faUser,
      maxLength: 500,
      validationRules: {
        type: "paragraphText",
        maxLength: 500,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 12 },
    },
    
   
  ];
  
  const videoFormFieldsEdit: ISVideoFormField[] = [
    {
      name: "title",
      label: "Title",
      type: "text",
      placeholder: "Enter Video Title",
      required: true,
      favImage: faFileText,
      maxLength: 50,
      validationRules: {
        type: "titleWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "video",
      label: "Upload video",
      type: "file",
      placeholder: "Upload New Video",
      required: false,
      favImage: faVideo,
     // maxLength: 50,
      accept: "video/mp4",
      validationRules: {
        type: "",
        maxSize: 104857600, 
        required: false,
        accept: "video/mp4",
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "description",
      label: "Video Description",
      type: "textarea",
      placeholder: "Video Description",
      required: false,
      favImage: faUser,
      maxLength: 500,
      validationRules: {
        type: "paragraphText",
        maxLength: 500,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 12 },
    },
    
   
  ];
  const handleVideoCancel = () => {
    dispatch(closeModal());
    setFormState({
      title:"",
      description: "",
      video: "",
    
    });
    setErrorState({
      title:"",
      description: "",
      video: "",
      
    });
  };
  const isFormValid = () => {
    const isAnyFieldEmpty = videoFormFields.some(
      (field: any) => field.required && !formState[field.name]
    );
    const hasErrors = Object.values(errorState).some(
      (error) => error && error.length > 0
    );
    return !isAnyFieldEmpty && !hasErrors;
  };

  const isEditFormValid = () => {
    const isAnyFieldEmpty = videoFormFieldsEdit.some((field: any) => {
      // Skip required check for video in edit mode
      // if (field.name === "video") {
      //   return false;
      // }
      return field.required && !formState[field.name];
    });
  
    const hasErrors = Object.values(errorState).some(
      (error) => error && error.length > 0
    );
  
    return !isAnyFieldEmpty && !hasErrors;
  };
  

  
  const renderModalContent = () => {
    switch (modalType) {
      case "add":
        return (
          <VideoForm
            formState={formState}
            setFormState={setFormState}
            errorState={errorState}
            setErrorState={setErrorState}
            onSubmit={() => {
                const getData = {
                  currentPage: page,
                  sort,
                  sortColumn,
                  searchText,
                  status,
                  perPage,
                };
                handleVideoCreation(
                  dispatch,
                  formState,
                  videoFormFields,
                  setErrorState,
                  null,
                  navigate,
                  location
                )
               
            }}
            isFormValid={isFormValid}
            fields={videoFormFields}
            dispatch={dispatch}
            onCancel={handleVideoCancel}
          />
        );
        case "edit":
          const isEdit = true;
        //  const filteredFields = videoFormFields.filter((field) => field.name !== "video");
          return (
            <VideoForm
              formState={formState}
              setFormState={setFormState}
              errorState={errorState}
              setErrorState={setErrorState}
              onSubmit={() => {
                  const getData = {
                    currentPage: page,
                    sort,
                    sortColumn,
                    searchText,
                    status,
                    perPage,
                  };
                  handleVideoUpdate(
                    dispatch,
                    formState,
                    videoFormFieldsEdit,
                    selectedData.id,
                    setErrorState,
                    getData,
                    navigate,
                    location
                  )
                 
              }}
              isFormValid={isEditFormValid}
              isEditModal={isEdit}
              fields={videoFormFieldsEdit}
              dispatch={dispatch}
              onCancel={handleVideoCancel}
            />
          );
     
        
      case "view":
        return (
          <VideoView
            selectedData={selectedData}
             onDelete={() =>
               dispatch(openModal({ type: "delete", data: selectedData }))
             }
            onEdit={() => handleModalOpen("edit", selectedData)}
            onClose={() => dispatch(closeModal())}
          />
        );
     
      default:
        return null;
    }
  };
  return (
    <div className="col-lg-12">
      <div className="card shadow-sm rounded-3">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h4 className="mb-0">
            {/* <FontAwesomeIcon icon={faFileMedical} className="me-2" /> */}
            <ThemeImage
              imageName="manageVideosImage"
              alt="manageVideosImage"
              className={
                currentTheme === "dark"
                  ? "dark-icon-image me-2"
                  : "light-icon-image me-2"
              }
            />
            Videos List
          </h4>
          <CommonButton
            type="button"
            className="filled-btn"
            text="Add"
            icon={<FontAwesomeIcon icon={faAdd} />}
            onClick={() => handleModalOpen("add", null)}
          />
        </div>

        <div className="table_top">
          <div className="search_outer">
            <input
              placeholder="Search Video"
              onChange={(e) => handleSearchInputChange(e.target.value)}
              
              value={localSearchText}
            />
            <div className="info-icon">
              <Tooltip
                title={`Search by Title,Created Date`}
              >
                <FontAwesomeIcon icon={faInfoCircle} className="fa-fw" />
              </Tooltip>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
          
            <CommonDropdown
              label="Per Page"
              options={[
                { label: "5", value: 5 },
                { label: "10", value: 10 },
                { label: "15", value: 15 },
              ]}
              selectedValue={localPerPage}
              onSelect={(value) => handlePerPageFilter(Number(value))}
            />
          </div>
        </div>

        <div className="card-body">
          <CommonTable
            columns={columns}
            data={formattedRecords}
            sortColumn={sortColumn}
            sortDirection={sort}
            onSort={handleSort}
            onPageChange={handlePageChange}
            totalPages={totalPages}
            currentPage={page}
            renderActions={renderActions}
            totalRecords={totalRecords}
          />
        </div> 
         <CommonModal
          show={isOpen && modalType !== "delete"}
          onHide={() => dispatch(closeModal())}
          title={
            modalType === "add"
              ? "Add New Video"
              : modalType === "edit"
              ? "Edit Video"
              : modalType === "view"
              ? "View Video"
            
              : ""
          }
        >
        {renderModalContent()}
        </CommonModal>
        <ConfirmationModal
          show={modalType === "delete"}
          onHide={() => dispatch(closeModal())}
          onConfirm={() => {
            const getData = {
              currentPage: page,
              sort,
              sortColumn,
              searchText,
              status,
              perPage,
            };
            handleVideoDeletion(
              dispatch,
              selectedData.id,
              getData,
              navigate,
              location,
              records
            );
          }}
          title="Confirm Delete"
          message="Are you sure you want to delete this video?"
        />
      </div>
    </div>
  );
};

export default VideosList;
