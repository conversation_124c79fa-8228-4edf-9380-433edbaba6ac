import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faEdit } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import { IVideoViewProps } from "../../../types/manage-video.type";
import { Button } from "react-bootstrap";
import config from "../../../config/config";

const VideoView: React.FC<IVideoViewProps> = ({
  selectedData,
  onDelete,
  onEdit,
  onClose,
}) => {


  return (
    <div>
      <div className="px-0 report_detail container">
        <div className="row row-gap-3">
          {[
            {
              label: "Title",
              value: selectedData.title,
            },
            {
              label: "Video",
              value: (
                <video width="500" height="300" controls crossOrigin="anonymous">
                  <source src={`${config.apiUrl}/videos/${selectedData.file_path}`} type={selectedData.mime_type} />
                  Your browser does not support the video tag.
                </video>
              ),
            },
            {
              label: "Created On",
              value: moment(selectedData.created_at).format("YYYY-MM-DD"),
            },
            { label: "Description", value: selectedData.description },
          ].map((detail, index) => (
            <div
              key={index}
              className={`${detail.label === "Video" ? "col-md-12" : "col-md-6"} ${detail.label.toLowerCase().replace(/\s+/g, '-')}`}

            >
              <div className="report_inn">
                <p className="mb-1 form-label">{detail.label}</p>
                <p className="txt">{detail.value}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="d-flex justify-content-between w-100 btn_wrap mt-3">
        <Button variant="outline-danger" className="danger-btn" onClick={onDelete}>
          <FontAwesomeIcon icon={faTrash} /> Delete
        </Button>
        <div className="btn_grp">
          <Button variant="primary" className="filled-btn" onClick={onEdit}>
            <FontAwesomeIcon icon={faEdit} /> Edit
          </Button>
          <Button className="outlined-btn" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VideoView;
