import React from "react";
import CommonForm from "../../common/form/CommonForm";
import CommonButton from "../../common/button/CommonButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSave } from "@fortawesome/free-solid-svg-icons";
import { useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import config from "../../../config/config";

const VideoForm: React.FC<any> = ({
  formState,
  setFormState,
  errorState,
  setErrorState,
  onSubmit,
  isFormValid,
  fields,
  isEditModal,
  onCancel,
}) => {
  const loading = useAppSelector((state: RootState) => state.loader.loader);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormState((prev: any) => ({
        ...prev,
        file, // for uploading new video
      }));
    }
  };

  return (
    <>
      {/* Render form fields (title + description) */}
      <CommonForm
        state={formState}
        setState={setFormState}
        errorState={errorState}
        setErrorState={setErrorState}
        fields={fields}
        onSubmit={onSubmit}
      />

      {/* Only in edit mode: show preview and upload */}
      {isEditModal && (
        <div className="mt-3">
          {/* Preview current video */}
          {formState?.file_path && (
            <div className="mb-3">
              <label className="form-label fw-bold">Current Video</label>
              <video width="100%" controls>
                
                 <source src={`${ config.apiUrl}/videos/${formState.file_path}`} type={formState.mime_type } />
                Your browser does not support the video tag.
              </video>
            </div>
          )}

        </div>
      )}

      {/* Buttons */}
      <div className="d-flex justify-content-end mt-3 gap-2">
        <CommonButton
          type="button"
          className="outlined-btn"
          text="Cancel"
          onClick={onCancel}
        />
        <CommonButton
          type="submit"
          className={!isFormValid() ? "disabled-btn" : "filled-btn"}
          text={isEditModal ? "Update" : "Submit"}
          icon={<FontAwesomeIcon icon={faSave} />}
          onClick={onSubmit}
          loading={loading}
          disabled={!isFormValid()}
        />
      </div>
    </>
  );
};

export default VideoForm;
