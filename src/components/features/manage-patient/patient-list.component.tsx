import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Column } from "../../../types/table.type";
import moment from "moment";
import PatientForm from "./PatientForm";
import PatientView from "./patient-view-modal.component"
import {
  ISPatientFormField,
  //Patient,
  IPatientRecord,
  IFetchPatientDetailsParams
  } from "../../../types/manage-patient.type ";
import {
  handlePatientCreation,
  handleFetchAllPatientDetails,
  handlePatientStatusChange,
  } from "../../../pages/manage-patient/manage-patient.event";
import { Tooltip,Switch } from "antd";
//import { MANAGE_PATIENT_APP_URL } from "../../../constants/appUrl";
import ThemeImage from "../../common/icons/ThemeImage";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle,faAdd, faUser } from "@fortawesome/free-solid-svg-icons";
import emailImage from "../../../assets/images/email.svg";
import CommonButton from "../../common/button/CommonButton";
import {openModal, closeModal } from "../../../redux/action";
import CommonModal from "../../common/Modal/CommonModal";
import CommonDropdown from "../../common/dropdown/CommonDropdown";
import CommonTable from "../../common/table/CommonTable";


const PatientList: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    records,
    page,
    totalPages,
    sort,
    sortColumn,
    searchText,
    perPage,
    totalRecords,
    status,
  } = useAppSelector((state: RootState) => state.ManagePatient);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  const [localStatus, setLocalStatus] = useState(status);
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const [localPerPage, setLocalPerPage] = useState(perPage);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const { isOpen, modalType, selectedData } = useAppSelector(
    (state: RootState) => state.modal
  );
  const navigate = useNavigate();

  useEffect(() => {
    
    const searchParams = new URLSearchParams(location.search);

    setLocalSearchText(searchParams.get("searchText") || "");
    const statusParam = searchParams.get("status");
    if (
      statusParam === "all" ||
      statusParam === "active" ||
      statusParam === "inactive"
    ) {
      setLocalStatus(statusParam);
    } else {
      setLocalStatus("all");
    }
    setLocalPerPage(Number(searchParams.get("perPage")) || 10);

    const initialParams: IFetchPatientDetailsParams = {
      currentPage: Number(searchParams.get("currentPage")) || 1,
      perPage: Number(searchParams.get("perPage")) || 10,
      sort: searchParams.get("sort") || "desc",
      sortColumn: searchParams.get("sortColumn") || "created_at",
      searchText: searchParams.get("searchText") || "",
      status: searchParams.get("status") || "all",
    };

    handleFetchAllPatientDetails(dispatch, initialParams, navigate, location);

    // eslint-disable-next-line
  }, [location.search]);
  
  
  const handleStatusFilter = (value: "all" | "active" | "inactive") => {
    setLocalStatus(value); // now `value` is correctly typed

    handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status: value,
        perPage,
      },
      navigate,
      location
    );
  };


  const columns: Column[] = [
    { key: "first_name", label: "First Name", sortable: true },
    {
      key: "last_name",
      label: "Last Name",
      sortable: true
    },
    {
      key: "email",
      label: "Email",
      sortable: true
    },
   
    { key: "created_at", label: "Created On", sortable: true },
    {
      key: "active_status",
      label: "Status",
      sortable: true,
      render: (value: boolean, item: IPatientRecord) => (
        <Switch
          checked={value}
          onChange={() => {
            const getData = {
              currentPage: page,
              sort,
              sortColumn,
              searchText,
              status,
              perPage,
            };
            handlePatientStatusChange(
              dispatch,
              item.id,
              !value,
              getData,
              navigate,
              location
            );
          }}
        />
      ),
    },
  ];
  const formattedRecords =
    records && records.length > 0
      ? records.map((patient: any) => ({
          ...patient,
          created_at: moment(patient.created_at).format("YYYY-MM-DD"),
         
        }))
      : [];

  const renderActions = (data: any) => (
    <div className="actions_wrap">
      {/* <Tooltip title={`View Patient Details`}>
        <button
          className="action_btn"
          onClick={() => {
            const url = MANAGE_PATIENT_APP_URL.replace(":patientID", user.id);
            navigate(url, {
              state: { data: user.id },
            });
          }}
        >
          <ThemeImage imageName="viewImage" />
        </button>
      </Tooltip> */}
      <Tooltip title={`View Patient Details`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "view", data }))}
        >
          <ThemeImage imageName="viewImage" />
        </button>
      </Tooltip>
    </div>
  );

  const handleSearchInputChange = (value: string) => {
    setLocalSearchText(value);

    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300); // Adjust the delay as needed (300 ms in this case)

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
    handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: value,
        status,
        perPage,
      },
      navigate,
      location
    );
  };


  const handlePerPageFilter = (value: number) => {
    setLocalPerPage(Number(value));

    handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status: localStatus,
        perPage: Number(value),
      },
      navigate,
      location
    );
  };

  const handleSort = (column: string) => {
    const newDirection = sort === "asc" ? "desc" : "asc";

    handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: page,
        sort: newDirection,
        sortColumn: column,
        searchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePageChange = (selectedItem: { selected: number }) => {
    handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: selectedItem.selected + 1,
        sort,
        sortColumn,
        searchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };
  const [formState, setFormState] = useState<Record<string, string>>({
    email:"",
    firstName: "",
    lastName: "",
  });

  const [errorState, setErrorState] = useState<Record<string, string>>({
    email:"",
    firstName: "",
    lastName: "",
  });
  const handleModalOpen = (type: string, data: null | null) => {
    if (type === "add") {
      setFormState({
        email: "",
        firstName: "",
        lastName: "",
      });
      dispatch(openModal({ type: "add" }));
    } else {
    //   setFormState({
    
    //     firstName: data?.firstName ? data?.firstName : "",
   //      lastName: data?.lastName ? data?.lastName : "",
    //     email: data?.email ? data?.email : "",
    //   });
     // dispatch(openModal({ type: "edit", data }));
    }
  };
  const patientFormFields: ISPatientFormField[] = [
    {
      name: "firstName",
      label: "First name",
      type: "text",
      placeholder: "Enter First Name",
      required: true,
      favImage: faUser,
      maxLength: 50,
      validationRules: {
        type: "nameWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "lastName",
      label: "Last name",
      type: "text",
      placeholder: "Enter Last Name",
      required: true,
      favImage: faUser,
      maxLength: 50,
      validationRules: {
        type: "nameWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "email",
      label: "Email",
      type: "text",
      placeholder: "Enter Email",
      required: true,
      image: emailImage,
      maxLength: 50,
      validationRules: {
        type: "email",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    }
   
  ];
  const handlePatientCancel = () => {
    dispatch(closeModal());
    setFormState({
      firstName: "",
      lastName: "",
      email:""
    
    });
    setErrorState({
      firstName: "",
      lastName: "",
      email:""
      
    });
  };
  const isFormValid = () => {
    const isAnyFieldEmpty = patientFormFields.some(
      (field: any) => field.required && !formState[field.name]
    );
    const hasErrors = Object.values(errorState).some(
      (error) => error && error.length > 0
    );
    return !isAnyFieldEmpty && !hasErrors;
  };
  const renderModalContent = () => {
    switch (modalType) {
      case "add":
        return (
          <PatientForm
            formState={formState}
            setFormState={setFormState}
            errorState={errorState}
            setErrorState={setErrorState}
            onSubmit={() => {
                const getData = {
                  currentPage: page,
                  sort,
                  sortColumn,
                  searchText,
                  status,
                  perPage,
                };
                handlePatientCreation(
                  dispatch,
                  formState,
                  patientFormFields,
                  setErrorState,
                  null,
                  navigate,
                  location
                )
               
            }}
            isFormValid={isFormValid}
            fields={patientFormFields}
            dispatch={dispatch}
            onCancel={handlePatientCancel}
          />
        );
      case "view":
        return (
          <PatientView
            selectedData={selectedData}
            // onDelete={() =>
            //   dispatch(openModal({ type: "delete", data: selectedData }))
            // }
           // onEdit={() => handleModalOpen("edit", selectedData)}
            onClose={() => dispatch(closeModal())}
          />
        );
     
      default:
        return null;
    }
  };
  return (
    <div className="col-lg-12">
      <div className="card shadow-sm rounded-3">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h4 className="mb-0">
            {/* <FontAwesomeIcon icon={faFileMedical} className="me-2" /> */}
            <ThemeImage
              imageName="managePatientImage"
              alt="managePatientImage"
              className={
                currentTheme === "dark"
                  ? "dark-icon-image me-2"
                  : "light-icon-image me-2"
              }
            />
            Patient List
          </h4>
          <CommonButton
            type="button"
            className="filled-btn"
            text="Add"
            icon={<FontAwesomeIcon icon={faAdd} />}
            onClick={() => handleModalOpen("add", null)}
          />
        </div>

        <div className="table_top">
          <div className="search_outer">
            <input
              placeholder="Search Patient"
              onChange={(e) => handleSearchInputChange(e.target.value)}
              // style={{ width: 300 }}
              value={localSearchText}
            />
            <div className="info-icon">
              <Tooltip
                title={`Search by First name,Last name,Email,Created Date`}
              >
                <FontAwesomeIcon icon={faInfoCircle} className="fa-fw" />
              </Tooltip>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
          <CommonDropdown
              label="Status"
              options={[
                { label: "All", value: "all" },
                { label: "Active", value: "active" },
                { label: "Inactive", value: "inactive" },
              ]}
              selectedValue={localStatus}
              onSelect={(value) => handleStatusFilter(value as any)}
            />
            <CommonDropdown
              label="Per Page"
              options={[
                { label: "5", value: 5 },
                { label: "10", value: 10 },
                { label: "15", value: 15 },
              ]}
              selectedValue={localPerPage}
              onSelect={(value) => handlePerPageFilter(Number(value))}
            />
          </div>
        </div>

        <div className="card-body">
          <CommonTable
            columns={columns}
            data={formattedRecords}
            sortColumn={sortColumn}
            sortDirection={sort}
            onSort={handleSort}
            onPageChange={handlePageChange}
            totalPages={totalPages}
            currentPage={page}
            renderActions={renderActions}
            totalRecords={totalRecords}
          />
        </div> 
         <CommonModal
          show={isOpen && modalType !== "delete"}
          onHide={() => dispatch(closeModal())}
          title={
            modalType === "add"
              ? "Add New Patient"
             
              : modalType === "view"
              ? "View Patient"
            
              : ""
          }
        >
        {renderModalContent()}
        </CommonModal>
      </div>
    </div>
  );
};

export default PatientList;
