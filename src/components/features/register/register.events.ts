import { AppDispatch } from "../../../redux/store";
import { NavigateFunction } from "react-router-dom";
import { LOGIN_APP_URL  } from "../../../constants/appUrl"; // e.g., "/dashboard" or "/login"
// import { registerUserApi } from "../../../services/auth.service"; // You must implement this
import authServices from "../../../services/endpoints/authenticate.api";
import { setLoader } from "../../../redux/action";
import flashMessage from "../../../utils/notifications/antdMessageUtils";
import { RegisterErrorState, RegisterField, RegisterState } from "../../../types/register.type";

interface HandleRegisterSubmitProps {
  registerState: RegisterState;
  dispatch: AppDispatch;
  navigate: NavigateFunction;
  setErrRegisterState: React.Dispatch<React.SetStateAction<RegisterErrorState>>;
  fields: RegisterField[];
}

export const handleRegisterSubmit = async ({
  registerState,
  dispatch,
  navigate,
  setErrRegisterState,
  fields,
}: HandleRegisterSubmitProps) => {
  let hasError = false;
  const errorObj: RegisterErrorState = {
    firstName: "",
    lastName: "",
    email: "",
  };

  // Field-level validation
  fields.forEach((field) => {
    const value = registerState[field.name];
    const rules = field.validationRules;

    if (rules.required && !value) {
      errorObj[field.name] = `${field.label} is required`;
      hasError = true;
      return;
    }

    if (rules.maxLength && value.length > rules.maxLength) {
      errorObj[field.name] = `${field.label} should be less than ${rules.maxLength} characters`;
      hasError = true;
    }

    if (rules.minLength && value.length < rules.minLength) {
      errorObj[field.name] = `${field.label} should be at least ${rules.minLength} characters`;
      hasError = true;
    }

    if (rules.type === "email" && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        errorObj[field.name] = "Invalid email format";
        hasError = true;
      }
    }
  });

  if (hasError) {
    setErrRegisterState(errorObj);
    return;
  }

 

  try {
    dispatch(setLoader(true));
    const response = await authServices.registerUserApi(registerState); // You must define this function
    
    if (response.success) {
      flashMessage(response.message, "success");
      navigate(LOGIN_APP_URL);
    } else {
      flashMessage(response.message, response.success ? "success" : "error");
      // setErrRegisterState({
      //   ...errorObj,
      //   general: response.message || "Registration failed. Please try again.",
      // });
    }
  } catch (error: any) {
    flashMessage( error?.message || "Something went wrong. Please try again.", "error");
  } finally {
    dispatch(setLoader(false));
  }
};
