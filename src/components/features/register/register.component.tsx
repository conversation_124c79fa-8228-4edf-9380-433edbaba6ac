import { <PERSON>, useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { useState } from "react";
import { RootState } from "../../../redux/store";
import CommonForm from "../../common/form/CommonForm";
import CommonButton from "../../common/button/CommonButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUserPlus } from "@fortawesome/free-solid-svg-icons";
 // You’ll need to define these types
import emailImage from "../../../assets/images/email.svg";
import { LOGIN_APP_URL } from "../../../constants/appUrl";
import passwordLockImage from "../../../assets/images/passwordLock.svg";
// import userImage from "../../../assets/images/user.svg"; // Add or replace with your full name icon
import { RegisterErrorState, RegisterField, RegisterState } from "../../../types/register.type";
import { handleRegisterSubmit } from "./register.events";

const RegisterComponent: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [registerState, setRegisterState] = useState<RegisterState>({
    firstName: "",
    lastName: "",
    email: "",
  });

  const [errRegisterState, setErrRegisterState] = useState<RegisterErrorState>({
    firstName: "",
    lastName: "",
    email: "",
  });

  const loading = useAppSelector((state: RootState) => state.loader.loader);

  const fields: RegisterField[] = [
    {
      name: "firstName",
      label: "First Name",
      type: "text",
        placeholder: "Enter first name",
        required: true,
        image: "",
        maxLength: 50,
        validationRules: {
            type: "text",
            minLength: 3,
            maxLength: 50,
            required: true,
        },
      },{
        name: "lastName",
        label: "Last Name",
        type: "text",
        placeholder: "Enter last name",
        required: true,
        image: "",
        maxLength: 50,
        validationRules: {
          type: "text",
          minLength: 3,
          maxLength: 50,
          required: true,
        },
      },
    {
      name: "email",
      label: "Email",
      type: "email",
      placeholder: "Enter email",
      required: true,
      image: emailImage,
      maxLength: 255,
      validationRules: {
        type: "email",
        minLength: 3,
        maxLength: 100,
        required: true,
      },
    },
  ];

  const isFormValid = (): boolean => {
    const isAnyFieldEmpty = fields.some(
      (field) => field.required && !registerState[field.name]
    );

    const hasErrors = Object.values(errRegisterState).some(
      (error:any) => error && error.length > 0
    );

    return !isAnyFieldEmpty && !hasErrors;
  };

  const onSubmit = () => {
    handleRegisterSubmit({
      registerState,
      dispatch,
      navigate,
      setErrRegisterState,
      fields,
    });
  };

  return (
    <div className="right-content">
      <div className="form-outer">
        <h2 className="mb-3">Register</h2>
        <CommonForm
          state={registerState}
          setState={setRegisterState}
          errorState={errRegisterState}
          setErrorState={setErrRegisterState}
          fields={fields}
          onSubmit={onSubmit}
        />
        {errRegisterState.general && (
          <div className="text-danger mb-3">{errRegisterState.general}</div>
        )}

        <div className="d-flex justify-content-end mt-3">
          <CommonButton
            type="submit"
            className={!isFormValid() ? "disabled-btn w-100" : "filled-btn w-100"}
            text="Register"
            icon={<FontAwesomeIcon icon={faUserPlus} />}
            onClick={onSubmit}
            loading={loading}
            disabled={!isFormValid()}
          />
        </div>
        <div className="d-flex justify-content-center align-items-baseline mt-2">
          <span className="me-1" style={{ fontWeight: 400 ,color: "#808080"}}>
            Already have an account ?
          </span>
          <Link
            className="form-label"
            style={{
              fontWeight: 400,
              textDecoration: "none",
              lineHeight: "1.5", 
              display: "inline-block", 
              verticalAlign: "baseline" 
            }}
            to={LOGIN_APP_URL}
          >
            Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RegisterComponent;
