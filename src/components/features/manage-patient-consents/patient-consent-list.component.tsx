import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Column } from "../../../types/table.type";
import moment from "moment";
import {
   IFetchPatientConsentDetailsParams
  } from "../../../types/manage-patient-consent.type";
import { 
  handleFetchAllPatientConsentDetails,
  } from "../../../pages/manage-patient-consent/manage-patient-consent.event";
import { Tooltip } from "antd";
//import { MANAGE_PATIENT_APP_URL } from "../../../constants/appUrl";
import ThemeImage from "../../common/icons/ThemeImage";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle,faAdd, faUser } from "@fortawesome/free-solid-svg-icons";

import {openModal, closeModal } from "../../../redux/action";
import CommonModal from "../../common/Modal/CommonModal";
import CommonDropdown from "../../common/dropdown/CommonDropdown";
import CommonTable from "../../common/table/CommonTable";
import ConfirmationModal from "../../common/Modal/ConfirmationModal";


const PatientConsentList: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    records,
    page,
    totalPages,
    sort,
    sortColumn,
    searchText,
    perPage,
    totalRecords,
    status,
  } = useAppSelector((state: RootState) => state.managePatientConsent);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  const [localStatus, setLocalStatus] = useState(status);
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const [localPerPage, setLocalPerPage] = useState(perPage);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const { isOpen, modalType, selectedData } = useAppSelector(
    (state: RootState) => state.modal
  );
  const navigate = useNavigate();

  useEffect(() => {
    
    const searchParams = new URLSearchParams(location.search);

    setLocalSearchText(searchParams.get("searchText") || "");
    const statusParam = searchParams.get("status");
    if (
      statusParam === "all" ||
      statusParam === "assigned" ||
      statusParam === "submitted" ||
      statusParam === "approved"
    ) {
      setLocalStatus(statusParam);
    } else {
      setLocalStatus("all");
    }
    setLocalPerPage(Number(searchParams.get("perPage")) || 10);

    const initialParams: IFetchPatientConsentDetailsParams = {
      currentPage: Number(searchParams.get("currentPage")) || 1,
      perPage: Number(searchParams.get("perPage")) || 10,
      sort: searchParams.get("sort") || "desc",
      sortColumn: searchParams.get("sortColumn") || "assigned_at",
      searchText: searchParams.get("searchText") || "",
      status: searchParams.get("status") || "all",
    };

    handleFetchAllPatientConsentDetails(dispatch, initialParams, navigate, location);

    // eslint-disable-next-line
  }, [location.search]);
  
  
  const handleStatusFilter = (value: "all" | "assigned" | "submitted" | "approved") => {
    setLocalStatus(value); // now `value` is correctly typed

    handleFetchAllPatientConsentDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status: value,
        perPage,
      },
      navigate,
      location
    );
  };


  const columns: Column[] = [
    { key: "econsent_title", label: "Consent title", sortable: true },
    {
      key: "patient_name",
      label: "Patient Name",
      sortable: true
    },
    {
      key: "patient_email",
      label: (
        <div className="d-flex align-items-center gap-2">
          Patient Email
          <Tooltip title="Email address of the patient assigned to this consent form">
            <FontAwesomeIcon icon={faInfoCircle} className="text-muted" style={{ fontSize: '12px' }} />
          </Tooltip>
        </div>
      ),
      sortable: true
    },
    {
      key: "coordinators",
      label: (
        <div className="d-flex align-items-center gap-2">
          Coordinators
          <Tooltip title="Research coordinators responsible for managing this consent form">
            <FontAwesomeIcon icon={faInfoCircle} className="text-muted" style={{ fontSize: '12px' }} />
          </Tooltip>
        </div>
      ),
      sortable: false
    },
   
    { key: "assigned_at", label: "Assigned On", sortable: true },
    {
      key: "status",
      label: "Status",
      sortable: true,

    }
  ];
  
   const formattedRecords =
      records && records.length > 0
        ? records.map((econsent: any) => {
           
           
            const coordinatorList = Array.isArray(econsent.coordinators)
            ? econsent.coordinators.map((coordinator: any, index: number) => {
                const fullName = `${coordinator.name  || ""}`.trim();
                return <li key={index}>{fullName}</li>;
              })
            : <li>-</li>;



            // Determine status display text
            const getStatusDisplayText = (status: string) => {
              switch (status) {
                case "assigned":
                  return "PENDING SUBMISSION";
                case "submitted":
                  return "SUBMITTED BY PATIENT";
                case "approved":
                  return "APPROVED";
                default:
                  return status;
              }
            };

            const getStatusDisplay = (status: string) =>{
              switch (status) {
                case "assigned":
                  return "PENDING FROM PATIENT";
                case "submitted":
                  return "PENDING FROM COORDINATOR";
                case "approved":
                  return "APPROVED";
                default:
                  return status;
              }
            }
            

            // Determine coordinator status
            const getCoordinatorStatus = (status: string) => {
              switch (status) {
                case "assigned":
                  return "PATIENT NOT STARTED";
                case "submitted":
                  return "NEED APPROVAL";
                case "approved":
                  return "APPROVED BY COORDINATOR";
                default:
                  return status;
              }
            };

            return {
              ...econsent,
              patient_name: econsent.patients[0]?.name || "-",
              patient_email: (
                <div className="d-flex align-items-center gap-2">
                  <span className="text-truncate">{econsent.patients[0]?.email || "-"}</span>
                  <Tooltip title={getStatusDisplayText(econsent.status)}>
                    <span
                      className={`status-indicator-enhanced ${econsent.status}`}
                      style={{
                        width: '12px',
                        height: '12px',
                        borderRadius: '50%',
                        backgroundColor:
                        getStatusDisplayText(econsent.status) === 'PENDING SUBMISSION' ? '#ff9f43' :
                        getStatusDisplayText(econsent.status) === 'SUBMITTED BY PATIENT' ? '#017a01a6' :
                        getStatusDisplayText(econsent.status) === 'APPROVED' ? '#017a01a6' : '#017a01a6',
                        flexShrink: 0,
                        cursor: 'pointer',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                      }}
                    ></span>
                  </Tooltip>
                </div>
              ),
              econsent_status: econsent.status,
              coordinator_list: econsent.coordinators,
              assigned_at: moment(econsent.assigned_at).format("YYYY-MM-DD"),

              coordinators: (
                <div className="d-flex align-items-start gap-2">
                  <ul className="mb-0 ps-0" style={{ listStyle: 'none' }}>
                    {coordinatorList}
                  </ul>
                  <Tooltip title={getCoordinatorStatus(econsent.status)}>
                    <span
                      className={`status-indicator-enhanced ${econsent.status}`}
                      style={{
                        width: '12px',
                        height: '12px',
                        borderRadius: '50%',
                        backgroundColor:
                        getCoordinatorStatus(econsent.status) === 'PATIENT NOT STARTED' ? '#ff9f43' :
                        getCoordinatorStatus(econsent.status) === 'NEED APPROVAL' ? '#ff9f43' :
                        getCoordinatorStatus(econsent.status) === 'APPROVED BY COORDINATOR' ? '#017a01a6' : '#017a01a6',
                        flexShrink: 0,
                        cursor: 'pointer',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        marginTop: '6px'
                      }}
                    ></span>
                  </Tooltip>
                </div>
              ),
              status: (
                <span className={`custom-badge badge ${econsent.status}`}>
                  {getStatusDisplay(econsent.status)}
                </span>
              ),
              coordinator_status: (
                <span className={`custom-badge badge ${econsent.status !== "approved" ? "need-approval" : "approved"}`}>
                  {getCoordinatorStatus(econsent.status)}
                </span>
              ),


            };
          })
    : [];

  const renderActions = (data: any) => (
    <div className="actions_wrap">
      <Tooltip title={`View Econsent Details`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "view", data }))}
        >
          <ThemeImage imageName="viewImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Counter Sign Econsent`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "econsentCounterSign", data }))}
          disabled={data.econsent_status === "assigned" || data.econsent_status === "approved"}
        >
          <ThemeImage imageName="manageResearchImage" />
        </button>
      </Tooltip>
    </div>
  );

  const handleSearchInputChange = (value: string) => {
    setLocalSearchText(value);

    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300); // Adjust the delay as needed (300 ms in this case)

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
    handleFetchAllPatientConsentDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: value,
        status,
        perPage,
      },
      navigate,
      location
    );
  };


  const handlePerPageFilter = (value: number) => {
    setLocalPerPage(Number(value));

    handleFetchAllPatientConsentDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status: localStatus,
        perPage: Number(value),
      },
      navigate,
      location
    );
  };

  const handleSort = (column: string) => {
    const newDirection = sort === "asc" ? "desc" : "asc";

    handleFetchAllPatientConsentDetails(
      dispatch,
      {
        currentPage: page,
        sort: newDirection,
        sortColumn: column,
        searchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePageChange = (selectedItem: { selected: number }) => {
    handleFetchAllPatientConsentDetails(
      dispatch,
      {
        currentPage: selectedItem.selected + 1,
        sort,
        sortColumn,
        searchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };
 
  const renderModalContent = () => {
    switch (modalType) {
    
      case "view":
        return (
          ""
        );
     
      default:
        return null;
    }
  };
  return (
    <div className="col-lg-12">
      <div className="card shadow-sm rounded-3">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h4 className="mb-0">
            {/* <FontAwesomeIcon icon={faFileMedical} className="me-2" /> */}
            <ThemeImage
              imageName="managePatientImage"
              alt="managePatientImage"
              className={
                currentTheme === "dark"
                  ? "dark-icon-image me-2"
                  : "light-icon-image me-2"
              }
            />
            Patient Consents List
          </h4>
         
        </div>

        <div className="table_top">
          <div className="search_outer">
            <input
              placeholder="Search Patient Consent"
              onChange={(e) => handleSearchInputChange(e.target.value)}
              // style={{ width: 300 }}
              value={localSearchText}
            />
            <div className="info-icon">
              <Tooltip
                title={`Search by Consent Title,Patient name,Patient Email,Status`}
              >
                <FontAwesomeIcon icon={faInfoCircle} className="fa-fw" />
              </Tooltip>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
          <CommonDropdown
              label="Status"
              options={[
                { label: "All", value: "all" },
                { label: "Assigned", value: "assigned" },
                { label: "Submitted", value: "submitted" },
                { label: "Approved", value: "approved" },
              ]}
              selectedValue={localStatus}
              onSelect={(value) => handleStatusFilter(value as any)}
            />
            <CommonDropdown
              label="Per Page"
              options={[
                { label: "5", value: 5 },
                { label: "10", value: 10 },
                { label: "15", value: 15 },
              ]}
              selectedValue={localPerPage}
              onSelect={(value) => handlePerPageFilter(Number(value))}
            />
          </div>
        </div>

        {/* Status Legend */}
        <div className="px-3 py-2 border-bottom">
          <div className="d-flex align-items-center gap-3 flex-wrap">
            <span className="fw-semibold text-muted" style={{ fontSize: '14px' }}>Status:</span>
            <div className="d-flex align-items-center gap-2">
              <span
                className="status-indicator-enhanced assigned"
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: '#ff9f43',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              ></span>
              <span className="text-muted" style={{ fontSize: '12px' }}>Assigned - Pending Submission</span>
            </div>
            <div className="d-flex align-items-center gap-2">
              <span
                className="status-indicator-enhanced approved"
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: '#62A75C',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              ></span>
              <span className="text-muted" style={{ fontSize: '12px' }}>Approved</span>
            </div>
          </div>
        </div>

        <div className="card-body">
          <CommonTable
            columns={columns}
            data={formattedRecords}
            sortColumn={sortColumn}
            sortDirection={sort}
            onSort={handleSort}
            onPageChange={handlePageChange}
            totalPages={totalPages}
            currentPage={page}
            renderActions={renderActions}
            totalRecords={totalRecords}
          />
        </div> 
         {/* <CommonModal
          show={isOpen && modalType !== "econsentCounterSign"}
          onHide={() => dispatch(closeModal())}
          title={
            modalType === "add"
              ? "Add New Patient"
             
              : modalType === "view"
              ? "View Patient"
            
              : ""
          }
        >
        {renderModalContent()}
        </CommonModal> */}
        <ConfirmationModal
          show={modalType === "econsentCounterSign" }
          onHide={() => dispatch(closeModal())}
          onConfirm={() => {
            const getData = {
              currentPage: page,
              sort,
              sortColumn,
              searchText,
              status,
              perPage,
            };
            // modalType === "delete"
            //   ?  handleEconsentDeletion(
            //       dispatch,
            //       selectedData.id,
            //       getData,
            //       navigate,
            //       location,
            //       records
            //     )
            //   :  
            //  handleEconsentStatusChange(
            //   dispatch,
            //   selectedData.id,
            //   true,
            //   getData,
            //   navigate,
            //   location,
              
            // );
            
          }}
          title= "Confirm EConsent Counter sign."
          message="Are you sure you want to send econsent form to patient to sign agian? "
          
        />
      </div>
    </div>
  );
};

export default PatientConsentList;
