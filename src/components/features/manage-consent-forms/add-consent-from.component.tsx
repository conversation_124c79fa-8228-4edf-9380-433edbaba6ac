"use client";

import React from "react";

import { useLocation, useNavigate } from "react-router-dom";
import { useAppDispatch } from "../../../redux/hooks";
import type { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";

import { Button } from "react-bootstrap";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import videothumb from "../../../assets/images/video.jpg";
// import playbtn from "../../../assets/images/video-icon.svg";

import {
  faAdd,
  faArrowLeft,
  faCalendarAlt,
  faNavicon,
  faPen,
  faSearch,
  faTimes,
} from "@fortawesome/free-solid-svg-icons";
import { Form, Dropdown } from "react-bootstrap";
import {
  handleFetchEconsentDropDowndData,
  handleAddConsentForm
 } from "../../../pages/manage-econsent/manage-econsent.event";


const AddConsentForm: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

   const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
 
  const [highlightResearch, setHighlightResearch] = useState(false);
  const [highlightVideo, setHighlightVideo] = useState(false);
  const [highlightVideoDes, setHighlightVideoDes] = useState(false);
  const [highlightDeclaration, setHighlightDeclaration] = useState(false);
  const [highlightPatient, setHighlightPatient] = useState(false);
  const [highlightCoordinator, setHighlightCoordinator] = useState(false);
  const [highlightExpiry, setHighlightExpiry] = useState(false);
  const [includePersonalInfo, setIncludePersonalInfo] = useState(false);
 
  
  
const [patientSearch, setPatientSearch] = useState("");
const [declarationSearch, setDeclarationSearch] = useState("");
const [coordinatorSearch, setCoordinatorSearch] = useState("");
  const [currentStep, setCurrentStep] = useState(1);
  type Step1Errors = {
    consentTitle?:string;
    videoType?: string;
    videoDesc?: string;
    researchType?: string;
    
  };
  
  type Step2Errors = {
    declarations?: string;
    coordinatorList?:string;
    patientList?:string
    expiryDate?:string;
  };

  
  // Step 1 values
  const [consentTitle, setConsentTitle] = useState('');
  const [videoType, setVideoType] = useState('');
  const [videoDesc, setVideoDesc] = useState('');
  const [researchType, setResearchType] = useState('');
  
  // Step 2 values
 
  const [selectedDeclarationItems, setSelectedDeclarationItems] = useState<{ id: number; label: string }[]>([]);
  const [selectedCoordinatorList, setSelectedCoordinatorList] =useState<{ id: number; label: string }[]>([]);
  const [selectedPatientList, setSelectedPatientList] = useState<{ id: number; label: string }[]>([]);
  const [expiryDate, setExpiryDate] = useState('');
  
  
  const [step1Errors, setStep1Errors] = useState<Step1Errors>({});
  const [step2Errors, setStep2Errors] = useState<Step2Errors>({});

  // General error message display helper
 const renderError = (msg) => <div className="text-danger small mt-1">{msg}</div>;
 
const validateStep1 = () => {
  const errors: Step1Errors = {}; // ✅ Properly typed
  
  
  if (!consentTitle.trim()) {
    errors.consentTitle = "Consent Title is required.";
  } else {
    const trimmedTitle = consentTitle.trim();
    const titleWithSpaceRegex = /^[A-Za-z]+(?:\s[A-Za-z]+)*$/;
  
    if (trimmedTitle.length < 3) {
      errors.consentTitle = "Consent Title must be at least 3 characters long.";
    } else if (trimmedTitle.length > 50) {
      errors.consentTitle = "Consent Title must not exceed 50 characters.";
    } else if (!titleWithSpaceRegex.test(trimmedTitle)) {
      errors.consentTitle = "Consent Title can only contain letters and single spaces between words.";
    } else if (consentTitle !== trimmedTitle || /\s{2,}/.test(consentTitle)) {
      errors.consentTitle = "Consent Title cannot have leading/trailing or multiple consecutive spaces.";
    }
  }
  if (!videoType) errors.videoType = "Please select a video type.";
 
    if (!videoDesc.trim()) {
      errors.videoDesc = "Video description is required.";
    } else {
      const trimmedDescription = videoDesc.trim();
      const paragraphTextRegex = /^[A-Za-z0-9\s.,!?'"()\-:;]+$/;

      if (trimmedDescription.length < 10) {
        errors.videoDesc = "Video description must be at least 10 characters long.";
      } else if (trimmedDescription.length > 500) {
        errors.videoDesc = "Video description must not exceed 500 characters.";
      } else if (!paragraphTextRegex.test(trimmedDescription)) {
        errors.videoDesc = "Video description can only contain letters, numbers, spaces, and basic punctuation.";
      } else if (videoDesc !== trimmedDescription || /\s{2,}/.test(videoDesc)) {
        errors.videoDesc = "Video description cannot have leading/trailing or multiple consecutive spaces.";
      }

    }
  if (!researchType) errors.researchType = "Please select a Research.";
  
  setStep1Errors(errors);
  return Object.keys(errors).length === 0;
};

const validateStep2 = () => {
  const errors: Step2Errors = {}; // ✅ Properly typed
  if (selectedDeclarationItems.length === 0) {
    errors.declarations = "At least one declaration must be selected.";
  }
  if (selectedCoordinatorList.length === 0) {
    errors.coordinatorList = "At least one coordinator must be selected.";
  }
  if (selectedPatientList.length === 0) {
    errors.patientList = "At least one patient must be selected.";
  }
  if (!expiryDate) errors.expiryDate = "Please select a expiry date.";
  setStep2Errors(errors);
  return Object.keys(errors).length === 0;
};

const handleNextStep = () => {
  if (currentStep === 1 && validateStep1()) {
    setCurrentStep(2);
  } else if (currentStep === 2 && validateStep2()) {
    setCurrentStep(3);
  }
};


  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStartOver = () => {
    setCurrentStep(1);
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
   
  }, [location.search]);
  

  

  const [declarationOptions, setDeclarationOptions] = useState([]);
  const [patientOptions, setPatientOptions] = useState([]);
  const [coordinatorOptions, setCoordinatorOptions] = useState([]);
  const [videosListing, setVideosListing] = useState([]);
  const [researchListing, setResearchListing] = useState([]);

  useEffect(() => {
    const fetchEconsentDropDowndData = async () => {
      const responseData = await handleFetchEconsentDropDowndData(dispatch);
      const { declarations, patients, coordinators ,videos ,research} = responseData;
      setVideosListing(videos || []); 
      setResearchListing(research || []);
  
      setDeclarationOptions(
        declarations.map((item) => ({
          id: item.id,
          label: item.title,
          type: "declaration",
        }))
      );
  
      setPatientOptions(
        patients.map((item) => ({
          id: item.id,
          label: `${item.first_name} ${item.last_name}`,
          type: "patient",
        }))
      );
  
      setCoordinatorOptions(
        coordinators.map((item) => ({
          id: item.id,
          label: `${item.first_name} ${item.last_name}`,
          type: "coordinator",
        }))
      );
    };
  
    fetchEconsentDropDowndData();
  }, [dispatch]);
  
 
  const filterBySearch = (list, searchValue) =>
    list.filter((item) =>
      item.label.toLowerCase().includes(searchValue.toLowerCase())
    );
  
  const filteredDeclarations = filterBySearch(declarationOptions, declarationSearch);
  const filteredPatients = filterBySearch(patientOptions, patientSearch);
  const filteredCoordinators = filterBySearch(coordinatorOptions, coordinatorSearch);

   const handleToggle = (
    item: { id: number; label: string },
    selectedItems: { id: number; label: string }[],
    setSelectedItems: React.Dispatch<React.SetStateAction<{ id: number; label: string }[]>>
  ) => {
    const exists = selectedItems.some((i) => i.id === item.id);
    if (exists) {
      setSelectedItems(selectedItems.filter((i) => i.id !== item.id));
    } else {
      setSelectedItems([...selectedItems, item]);
    }
  };
  
  
 
  
  const isNextDisabled = () => {
    if (currentStep === 1) {
      return !consentTitle || !videoType || !videoDesc.trim() || !researchType;
    }
  
    if (currentStep === 2) {
      return (
        selectedDeclarationItems.length === 0 ||
        selectedCoordinatorList.length === 0 ||
        selectedPatientList.length === 0 ||
        !expiryDate
      );
    }
  
    return false;
  };
 
 

  const handleSearchInputChange = (value: string) => {
    
    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300);

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
  
  };

  

  const handleBackClick = () => {
   
  };

 
  const redirectToStep2WithHighlight = (target: 'declaration' | 'patient' | 'expiry_date') => {
    setCurrentStep(2);
  
    setHighlightDeclaration(false);
    setHighlightPatient(false);
    setHighlightCoordinator(false);
    setHighlightExpiry(false);
  
    if (target === 'declaration') setHighlightDeclaration(true);
    if (target === 'patient'){
      setHighlightPatient(true);
      setHighlightCoordinator(true);
    } 
    if (target === 'expiry_date') setHighlightExpiry(true);
    
  };
  const redirectToStep1WithHighlight = (target:  'research' | 'video_des') => {
    setCurrentStep(1);
  
    setHighlightResearch(false);
    setHighlightVideoDes(false);
    setHighlightVideo(false);
  
    if (target === 'research') setHighlightResearch(true);
    if (target === 'video_des') setHighlightVideo(true);setHighlightVideoDes(true);
  };

  useEffect(() => {
    if (highlightDeclaration || highlightPatient || highlightCoordinator || highlightExpiry || highlightResearch || highlightVideo || highlightVideoDes) {
      const timer = setTimeout(() => {
        setHighlightDeclaration(false);
        setHighlightPatient(false);
        setHighlightCoordinator(false);
        setHighlightExpiry(false);
        setHighlightResearch(false);
        setHighlightVideoDes(false);
        setHighlightVideo(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [highlightDeclaration, highlightPatient, highlightCoordinator,highlightExpiry,highlightResearch,highlightVideo,highlightVideoDes]);
  
  const selectedResearch = researchListing.find((r) => String(r.id) === researchType);
  const selectedVideo = videosListing.find((r) => String(r.id) === videoType);
  
  useEffect(() => {
    console.log("Updated step2Errors:", step2Errors);
    
  }, [step2Errors]);
 
  const handleSubmit = async () => {
    const errors: Step2Errors = {};
    if( selectedDeclarationItems.length === 0 ||
      selectedCoordinatorList.length === 0 ||
      selectedPatientList.length === 0 ){
        if (selectedDeclarationItems.length === 0) {
          errors.declarations = "At least one declaration must be selected.";
        }
        if (selectedCoordinatorList.length === 0) {
          errors.coordinatorList = "At least one coordinator must be selected.";
        }
        if (selectedPatientList.length === 0) {
          errors.patientList = "At least one patient must be selected.";
        }
        setStep2Errors(errors);
        
      
        setCurrentStep(2);
        return;

    }
   
    const data = {
      title:consentTitle,
      video_id:  videoType ? parseInt(videoType, 10) : null,
      video_description:videoDesc,
      research_id:  researchType ? parseInt(researchType, 10) : null,
      personal_info_check:includePersonalInfo,
      declaration_ids: selectedDeclarationItems.map((i) => i.id), // [1, 2]
      patient_assigned: selectedPatientList.map((i) => i.id), //
      coordinator_ids: selectedCoordinatorList.map((i) => i.id), // [4]
      expiry_time: `${expiryDate}T23:59:59Z`
    };
    
  
    try {
      
      const responseData = await handleAddConsentForm(
                        dispatch,
                        data,
                        navigate,
                        location,
                        null
          );

  
      
     
      if (!responseData  ) {
        throw new Error("Ecosent not saved or invalid response.");
      }
        
      // Show success toast or reset form here
    } catch (error) {
      console.error("Error:", error);
      // Show error notification
    }
  };
  return (
    <div className="col-lg-12">
      <div className="user-details-container card  consent-forms">
        <div className="card-body d-flex flex-column">
          <div className="d-flex justify-content-start align-items-center mb-0 gap-3 main-head card-header p-0 pb-2">
            <Button
              onClick={currentStep === 1 ? handleBackClick : handlePreviousStep}
              className="filled-btn px-2"
            >
              <FontAwesomeIcon icon={faArrowLeft} />
            </Button>

            <h4 className="m-0">Add E-Consent</h4>
          </div>

          <div
            className={`consent-form-box first-step ${
              currentStep === 1 ? "d-block" : "d-none"
            }`}
          >
            <form>
            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Consent Title</h6>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12">
                <div className="form-group mb-3">
                  <label className="mb-1">
                    Title <span className="text-danger">*</span>
                  </label>
                  <div className= {`position-relative w-100 ${highlightExpiry ? 'highlight-border' : ''}`}>
                    <input
                      name="consent_title"
                      type="text"
                      placeholder="Consent Title"
                      className="form-control calender-input"
                      value={consentTitle}
                      onChange={(e) => setConsentTitle(e.target.value)}
                    />
                   
                    {step1Errors.consentTitle && renderError(step1Errors.consentTitle)}
                  </div>
                </div>
              </div>
            </div>
            <hr className="hr-line"></hr>
              <div className="row">
                <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                  <h6 className="mb-1">Add Video</h6>
                  <p>Choose an explanation video for this consent form.</p>
                </div>
                <div className="col-lg-9 col-md-8 col-sm-12">
                  <div className="form-group mb-3">
                    <label className="mb-1">
                      Select Video <span className="text-danger">*</span>
                    </label>
                    <select
                      name="response_type"
                      className={`form-control ${highlightVideo ? 'highlight-border' : ''}`}
                      value={videoType}
                      onChange={(e) => setVideoType(e.target.value)}
                    >
                      <option value="">Please Select</option>
                      {videosListing.map((video) => (
                        <option key={video.id} value={video.id}>
                          {video.title}
                        </option>
                      ))}
                    </select>
                    {step1Errors.videoType && renderError(step1Errors.videoType)}

                  </div>
                  <div className= "form-group mb-2">
                    <label className="mb-1">
                      Video Description <span className="text-danger">*</span>
                    </label>
                    {/* <textarea className="form-control"></textarea> */}
                    <textarea
                      className= {`form-control ${highlightVideoDes ? 'highlight-border' : ''}`}
                      value={videoDesc}
                      onChange={(e) => setVideoDesc(e.target.value)}
                    ></textarea>
                    {step1Errors.videoDesc && renderError(step1Errors.videoDesc)}
                  </div>
                </div>
              </div>

              <hr className="hr-line"></hr>
              <div className="row">
                <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                  <h6 className="mb-1">Attach a Research Program</h6>
                  <p>
                    Link this consent to one or more relevant research studies.
                  </p>
                </div>
                <div className="col-lg-9 col-md-8 col-sm-12">
                  <div className="form-group mb-3 ">
                    <label className="mb-1">
                      Select Research Program{" "}
                      <span className="text-danger">*</span>
                    </label>
                    <select name="research_type" className={`form-control ${highlightResearch ? 'highlight-border' : ''}`}
                       value={researchType}
                       onChange={(e) => setResearchType(e.target.value)}>
                      <option value="">
                        Please select Research
                      </option>
                      {researchListing.map((research) => (
                        <option key={research.id} value={research.id}>
                          {research.title}
                        </option>
                      ))}
                    </select>
                    {step1Errors.researchType && renderError(step1Errors.researchType)}
                  </div>
                </div>
              </div>

              <hr className="hr-line"></hr>
              <div className="row">
                <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                  <h6 className="mb-1">Personal Information</h6>
                  <p>
                    Link this consent to one or more relevant research studies.
                  </p>
                </div>
                <div className="col-lg-9 col-md-8 col-sm-12">
                  <div className="form-group mb-3">
                    <div className="form-check">
                      <input
                        name="is_personal_info"
                        className="form-check-input"
                        type="checkbox"
                        id="flexCheckChecked"
                        checked={includePersonalInfo}
                        onChange={(e) => setIncludePersonalInfo(e.target.checked)}
                      />
                      <label
                        className="form-check-label fw-medium fs-6"
                        htmlFor="flexCheckChecked"
                      >
                        Include personal information
                      </label>
                    </div>
                    <p>
                      Allowing this will auto-fill known details about the
                      patient in the form
                    </p>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div
            className={`consent-form-box second-step ${
              currentStep === 2 ? "d-block" : "d-none"
            }`}
          >
            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Attach Declaration</h6>
                <p>Include all declarations relevant to the consent form.</p>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12">
                <div className="form-group mb-3">
                  <label className="mb-1">
                    Select Declarations <span className="text-danger">*</span>
                  </label>
                  <Dropdown className={`w-100 ${highlightDeclaration ? 'highlight-border' : ''}`}>
                    <Dropdown.Toggle
                      className="w-100 form-control form-dropDown-toggle"
                      variant="light"
                    >
                      {selectedDeclarationItems.length > 0
                        ? selectedDeclarationItems.map((i) => i.label).join(", ")
                        : "Select Declarations"}
                    </Dropdown.Toggle>

                    <Dropdown.Menu
                      className="w-100  form-dropdown"
                      style={{ maxHeight: "200px", overflowY: "auto" }}
                    >
                      <div className="p-2 postion-relative">
                        <Form.Control
                          type="text"
                          placeholder="Search"
                          value={declarationSearch}
                          onChange={(e) => setDeclarationSearch(e.target.value)}
                          className="mb-0 searchInput"
                        />
                        <FontAwesomeIcon
                          icon={faSearch}
                          className="searchicon"
                        />
                      </div>
                      {filteredDeclarations.map((item, index) => (
                        <Form.Check
                          //key={index}
                          key={`decl-${item.id}`}
                          type="checkbox"
                          id={`decl-${item.id}`}
                          label={item.label}
                          //checked={selectedDeclarationItems.includes(item.id)}
                          checked={selectedDeclarationItems.some((i) => i.id === item.id)}
                          //onChange={() => handleToggle(item)}
                          onChange={() =>
                            handleToggle(item, selectedDeclarationItems, setSelectedDeclarationItems)
                          }
                          className="px-2 w-100 position-relative px-3 py-2 form-check-box"
                        />
                      ))}
                      
                    </Dropdown.Menu>
                  </Dropdown>
                  {step2Errors.declarations && renderError(step2Errors.declarations)}
                </div>
              </div>
            </div>

            <hr className="hr-line"></hr>
            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Assign Research Coordinator</h6>
                <p>
                  Select a staff member(s) to oversee this consent process &
                  choose patient(s) this consent form is intended for.
                </p>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12">
                <div className="row">
                  <div className="col-sm-12 col-md-6">
                    <div className="form-group">
                      <label className="mb-1">
                        Select coordinator
                        <span className="text-danger">*</span>
                      </label>
                      <Dropdown className={`w-100 ${highlightCoordinator ? 'highlight-border' : ''}`}>
                        <Dropdown.Toggle
                          className="w-100 form-control form-dropDown-toggle"
                          variant="light"
                        >
                          {selectedCoordinatorList.length > 0
                            ?selectedCoordinatorList.map((i) => i.label).join(", ")
                            : "Select Coordinator"}
                        </Dropdown.Toggle>

                        <Dropdown.Menu
                          className="w-100  form-dropdown"
                          style={{ maxHeight: "200px", overflowY: "auto" }}
                        >
                          <div className="p-2 postion-relative">
                            <Form.Control
                              type="text"
                              placeholder="Search"
                              value={coordinatorSearch}
                              onChange={(e) => setCoordinatorSearch(e.target.value)}
                              className="mb-0 searchInput"
                            />
                            <FontAwesomeIcon
                              icon={faSearch}
                              className="searchicon"
                            />
                          </div>
                          {filteredCoordinators.map((item, index) => (
                            <Form.Check
                              //key={index}
                              key={`coord-${item.id}`}
                              type="checkbox"
                              id={`coord-${item.id}`}
                              label={item.label}
                              checked={selectedCoordinatorList.some((i) => i.id === item.id)}
                             // checked={selectedCoordinatorList.includes(item.id)}
                             // onChange={() => handleToggle(item)}
                             onChange={() =>
                              handleToggle(item, selectedCoordinatorList, setSelectedCoordinatorList)
                             }
                              className="px-2 w-100 position-relative px-3 py-2 form-check-box"
                            />
                          ))}
                          
                        </Dropdown.Menu>
                      </Dropdown>
                      {step2Errors.coordinatorList && renderError(step2Errors.coordinatorList)}
                    </div>
                  </div>

                  <div className="col-sm-12 col-md-6">
                    <div className="form-group">
                      <label className="mb-1">
                        Select patient
                        <span className="text-danger">*</span>
                      </label>
                      <Dropdown className={`w-100 ${highlightPatient ? 'highlight-border' : ''}`}>
                        <Dropdown.Toggle
                          className="w-100 form-control form-dropDown-toggle"
                          variant="light"
                        >
                          {selectedPatientList.length > 0
                            ? selectedPatientList.map((i) => i.label).join(", ")
                            : "Select Patient"}
                        </Dropdown.Toggle>

                        <Dropdown.Menu
                          className="w-100  form-dropdown"
                          style={{ maxHeight: "200px", overflowY: "auto" }}
                        >
                          <div className="p-2 postion-relative">
                            <Form.Control
                              type="text"
                              placeholder="Search"
                              value={patientSearch}
                              onChange={(e) => setPatientSearch(e.target.value)}
                              className="mb-0 searchInput"
                            />
                            <FontAwesomeIcon
                              icon={faSearch}
                              className="searchicon"
                            />
                          </div>
                          {filteredPatients.map((item, index) => ( 
                          
                            <Form.Check
                            key={`ptnt-${item.id}`}
                            type="checkbox"
                            id={`ptnt-${item.id}`}
                            label={item.label}
                            checked={selectedPatientList.some((i) => i.id === item.id)}
                            onChange={() =>
                              handleToggle(item, selectedPatientList, setSelectedPatientList)
                            }
                            className="px-2 w-100 position-relative px-3 py-2 form-check-box"
                          />
                          ))}
                          

                        </Dropdown.Menu>
                      </Dropdown>

                      {step2Errors.patientList && renderError(step2Errors.patientList)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <hr className="hr-line"></hr>
            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Expiry of Consent</h6>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12">
                <div className="form-group mb-3">
                  <label className="mb-1">
                    Select expiry date <span className="text-danger">*</span>
                  </label>
                  <div className= {`position-relative w-100 ${highlightExpiry ? 'highlight-border' : ''}`}>
                    <input
                      name="expiry_date"
                      type="date"
                      placeholder="DD/MM/YYYY"
                      className="form-control calender-input"
                      min={new Date().toISOString().split("T")[0]} // sets today's date as minimum
                      value={expiryDate}
                      onChange={(e) => setExpiryDate(e.target.value)}
                    />
                    <FontAwesomeIcon
                      icon={faCalendarAlt}
                      className="calender-icon"
                    />
                    {step2Errors.expiryDate && renderError(step2Errors.expiryDate)}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            className={`consent-form-box third-step ${
              currentStep === 3 ? "d-block" : "d-none"
            }`}
          >
            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Video</h6>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12 d-flex gap-3 align-items-start videoSec ">
                <div className="desc d-flex gap-3">
                  {/* <div className="desc-Img">
                    <img src={videothumb} className="w-thumb"/>
                    <div className="overlayicon">
                      <img src={playbtn} />
                    </div>
                  </div> */}
                  <div className="">
                    <input className="form-control" value={selectedVideo?.title || ""}/>
                  </div>

                  <div className="description-box flex-grow">
                    <label>Description</label>
                    <p>{videoDesc}
                    {/* Video description */}
                     
                    </p>
                  </div>
                </div>

                <div className="min-100">
                  <button className="btnLink" onClick={() => redirectToStep1WithHighlight('video_des')}>
                    <FontAwesomeIcon icon={faPen} /> Edit
                  </button>
                </div>
              </div>
            </div>
            <hr className="hr-line"></hr>

            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Research Program</h6>
                <p className="mb-0">Drag to set the order in which items </p>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12 d-flex gap-3 align-items-start ">
                <div className="w-100 d-flex gap-3">
                   <input className="form-control" value={selectedResearch?.title || ""}/>
                </div>

                <div className="min-100">
                  <button className="btnLink" onClick={() => redirectToStep1WithHighlight('research')}>
                    <FontAwesomeIcon icon={faPen} /> Edit
                  </button>
                </div>
              </div>
            </div>
            <hr className="hr-line"></hr>

            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Personal Information</h6> 
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12 d-flex gap-3 align-items-start ">
                <div className="w-100 d-flex gap-3">
                   <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        id="flexCheckChecked"
                        checked={includePersonalInfo}
                        onChange={(e) => setIncludePersonalInfo(e.target.checked)}
                      />
                      <label
                        className="form-check-label fw-medium fs-6"
                        htmlFor="flexCheckChecked"
                      >
                        Include personal information
                      </label>
                    </div>
                </div>
 
              </div>
            </div>
            <hr className="hr-line"></hr>

             <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Declaration</h6> 
                <p>Drag to set the order in which items will appear in the patient’s view.</p>
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12 d-flex gap-3 align-items-start ">
                <div className="w-100 d-flex flex-wrap gap-2">
                {selectedDeclarationItems.map((item) => (
                  <div className="drag-box" key={item.id}>
                    <div className="d-flex gap-2 align-items-center">
                      <FontAwesomeIcon icon={faNavicon} />
                      {item.label}
                    </div>
                    <FontAwesomeIcon
                      icon={faTimes}
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        setSelectedDeclarationItems(
                          selectedDeclarationItems.filter((i) => i.id !== item.id)
                        )
                      }
                    />
                  </div>
                ))}


                </div>

                 <div className="min-100">
                  <button className="btnLink" onClick={() => redirectToStep2WithHighlight('declaration')}>
                    <FontAwesomeIcon icon={faAdd} /> Add
                  </button>
                </div>
 
              </div>
            </div>
            <hr className="hr-line"></hr>


            <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Research Coordinators & Patients</h6> 
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12 d-flex gap-3 align-items-start ">
                <div className="w-100 d-flex flex-wrap gap-3">
                  
                   <div className="tags w-100">
                      <label>Research Coordinator: </label>
                      <ul>
                      {/* {selectedCoordinatorList.map((item, index) => (
                                                
                        <li  key={index}> {item} 
                          <FontAwesomeIcon icon={faTimes}/>
                        </li>
                      
                      ))} */}
                      {selectedCoordinatorList.map((item) => (
                          <li key={item.id}>
                            {item.label}
                            <FontAwesomeIcon
                              icon={faTimes}
                              className="ml-2"
                              onClick={() =>
                                setSelectedCoordinatorList(selectedCoordinatorList.filter((i) => i.id !== item.id))
                              }
                              style={{ cursor: "pointer", marginLeft: "8px" }}
                            />
                          </li>
                        ))}
                      </ul>
                      
                   </div>

                   <div className="tags  w-100">
                      <label>Patient </label>
                      <ul>
                        {/* {selectedPatientList.map((item, index) => (
                          <li  key={index}> {item} 
                            <FontAwesomeIcon icon={faTimes}/>
                          </li>
                        
                        ))} */}
                        {selectedPatientList.map((item) => (
                          <li key={item.id}>
                            {item.label}
                            <FontAwesomeIcon
                              icon={faTimes}
                              className="ml-2"
                              onClick={() =>
                                setSelectedPatientList(selectedPatientList.filter((i) => i.id !== item.id))
                              }
                              style={{ cursor: "pointer", marginLeft: "8px" }}
                            />
                          </li>
                        ))}
                      </ul>
                   </div>
                </div>

                <div className="min-100">
                  <button className="btnLink" onClick={() => redirectToStep2WithHighlight('patient')}>
                    <FontAwesomeIcon icon={faPen} /> Edit
                  </button>
                </div>
              </div>
            </div>
            <hr className="hr-line"></hr>

             <div className="row">
              <div className="col-lg-3 col-md-4 col-sm-12 label-text">
                <h6 className="mb-1">Expiry of Consent</h6> 
              </div>
              <div className="col-lg-9 col-md-8 col-sm-12 d-flex gap-3 align-items-start ">
                <div className="w-100 d-flex flex-wrap gap-3">
                   <div className="tags w-100"> 
                      <ul>
                        <li>{expiryDate}
                        </li>
                      </ul>
                   </div>
 
                </div>

                <div className="min-100">
                  <button className="btnLink" onClick={() => redirectToStep2WithHighlight('expiry_date')}>
                    <FontAwesomeIcon icon={faPen} /> Edit
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="p-3 form-footer d-flex justify-content-between align-items-center gap-2 flex-wrap">
            <div className="step-indicator d-flex align-items-center">
              <div className="position-relative d-flex align-items-center justify-content-center">
                <svg width="24" height="24" className="progress-ring">
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    fill="none"
                    stroke="#D9D9D9"
                    strokeWidth="4"
                  />
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    fill="none"
                    stroke="#28a745"
                    strokeWidth="4"
                    strokeDasharray={`${2 * Math.PI * 10}`}
                    strokeDashoffset={`${
                      2 *
                      Math.PI *
                      10 *
                      (1 -
                        (currentStep === 1
                          ? 0.3333
                          : currentStep === 2
                          ? 0.66
                          : 1))
                    }`}
                    strokeLinecap="round"
                    transform="rotate(-90 12 12)"
                    style={{ transition: "stroke-dashoffset 0.3s ease" }}
                  />
                </svg>
              </div>
              <span className="ms-2 fw-medium">Step {currentStep}/3</span>
            </div>
            <div className="d-flex gap-2 gap-md-3">
              <Button onClick={handleStartOver} className="outlined-btn px-4">
                Start Over
              </Button>
            
              {currentStep < 3 ? (
                
                <Button
                onClick={handleNextStep}
                className="filled-btn px-4"
               disabled={isNextDisabled()}
              >
                Next
              </Button>
              ) : (
                <Button onClick={handleSubmit} className="filled-btn px-4">
                  Submit
                </Button>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddConsentForm;
