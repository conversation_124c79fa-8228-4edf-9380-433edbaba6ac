import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Column } from "../../../types/table.type";
import moment from "moment";
 import {
IFetchConsentFormDetailsParams
  } from "../../../types/manage-econsent.type";
 import {
   handleFetchAllConsentForms,
   handleEconsentDeletion,
   handleEconsentStatusChange,
  } from "../../../pages/manage-econsent/manage-econsent.event";
import { Tooltip } from "antd";
import ThemeImage from "../../common/icons/ThemeImage";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle,faAdd, faUser } from "@fortawesome/free-solid-svg-icons";
import CommonButton from "../../common/button/CommonButton";
import {openModal, closeModal } from "../../../redux/action";
import CommonModal from "../../common/Modal/CommonModal";
import CommonDropdown from "../../common/dropdown/CommonDropdown";
import CommonTable from "../../common/table/CommonTable";
import ConfirmationModal from "../../common/Modal/ConfirmationModal";

import { ADD_CONSENT_FORMS_APP_URL,EDIT_CONSENT_FORMS_APP_URL } from "../../../constants/appUrl";
import PDFViewer from "./PDFViewer";


const PatientList: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    records,
    page,
    totalPages,
    sort,
    sortColumn,
    searchText,
    perPage,
    totalRecords,
    status,
  } = useAppSelector((state: RootState) => state.ManageEconsent);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  const [localStatus, setLocalStatus] = useState(status);
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const [localPerPage, setLocalPerPage] = useState(perPage);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const { isOpen, modalType, selectedData } = useAppSelector(
    (state: RootState) => state.modal
  );
  const navigate = useNavigate();

  useEffect(() => {
    
    const searchParams = new URLSearchParams(location.search);

    setLocalSearchText(searchParams.get("searchText") || "");
    const statusParam = searchParams.get("status");
    if (
      statusParam === "all" ||
      statusParam === "draft" ||
      statusParam === "published"
    ) {
      setLocalStatus(statusParam);
    } else {
      setLocalStatus("all");
    }
    setLocalPerPage(Number(searchParams.get("perPage")) || 10);

    const initialParams: IFetchConsentFormDetailsParams = {
      currentPage: Number(searchParams.get("currentPage")) || 1,
      perPage: Number(searchParams.get("perPage")) || 10,
      sort: searchParams.get("sort") || "desc",
      sortColumn: searchParams.get("sortColumn") || "created_at",
      searchText: searchParams.get("searchText") || "",
      status: searchParams.get("status") || "all",
    };

    handleFetchAllConsentForms(dispatch, initialParams, navigate, location);

    // eslint-disable-next-line
  }, [location.search]);
  
  
  const handleStatusFilter = (value:  "all" | "draft" | "published") => {
    setLocalStatus(value); // now `value` is correctly typed

    handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status: value,
        perPage,
      },
      navigate,
      location
    );
  };

  
  const columns: Column[] = [
    { key: "title", label: "Consent title", sortable: true },
    { key: "video_title", label: "Video title", sortable: false },
    {
      key: "research_title",
      label: "Research Title",
      sortable: false
    },
    {
      key: "coordinators",
      label: "Coordinator(s)",
      sortable: false
    },
    {
      key: "patient_assigned",
      label: "Patient(s)",
      sortable: false
    },
   
    { key: "created_at", label: "Created On", sortable: true },
    { key: "expiry_time", label: "Expiry Date", sortable: true },
    {
      key: "is_published",
      label: "Status",
      sortable: true,
      
     
    },
  ];
  
  const formattedRecords =
    records && records.length > 0
      ? records.map((econsent: any) => {
          const isPublished =
          econsent.is_published === true ||
          econsent.is_published === "true" ||
          econsent.is_published === 1;
         
          const coordinatorList = Array.isArray(econsent.coordinators)
          ? econsent.coordinators.map((coordinator: any, index: number) => {
              const fullName = `${coordinator.label || ""}`.trim();
              return <li key={index}>{fullName}</li>;
            })
          : <li>-</li>;
          
          const patientList = Array.isArray(econsent.patient_assigned)
          ? econsent.patient_assigned.map((patient: any, index: number) => {
              const fullName = `${patient.label || ""}`.trim();
              return <li key={index}>{fullName}</li>;
            })
          : <li>-</li>;
  
          return {
            ...econsent,
            research_title: econsent.research?.title || "-",
            video_title: econsent.video?.title || "-",
            is_published_flag: isPublished,
            coordinator_list: econsent.coordinators,
            patient_assigned_list: econsent.patient_assigned,
            created_at: moment(econsent.created_at).format("YYYY-MM-DD"),
            expiry_time: moment(econsent.expiry_time).format("YYYY-MM-DD"),
            is_published: (
              <span className={`custom-badge badge ${isPublished ? "published" : "draft"}`}>
                {isPublished ? "Send E-consent" : "Draft"}
              </span>
            ),
            coordinators: (
              <ul className="mb-0 ps-3">
                {coordinatorList}
              </ul>
            ),
            patient_assigned: (
              <ul className="mb-0 ps-3">
                {patientList}
              </ul>
            ),
           
          };
        })
  : [];
 
  const renderActions = (data:any) => (
    <div className="actions_wrap">
      <Tooltip title={`Edit EConsent`}>
        <button
          className="action_btn"
          disabled={data.is_published_flag === true}
          onClick={() => {
            const url = EDIT_CONSENT_FORMS_APP_URL.replace(":consentFormId", data.id);
            navigate(url, {
              //state: { data: data.id },
              state: { 
                  consentData: {
                      id:data.id,
                      title:data.title,
                      video_id:  data.video.id,
                      video_description:data.video_description,
                      research_id:   data.research.id ,
                      research_name:   data.research.title ,
                      personal_info_check:data.personal_info_check,
                      declarations: data.declarations,
                      patient_assigned_data: data.patient_assigned_list, //
                      coordinator_data: data.coordinator_list, // [4]
                      expiry_time: data.expiry_time
                  }
                
               },
            });
          }}
        >
          <ThemeImage imageName="editImage" />
        </button>
      </Tooltip>
     
      <Tooltip title={`Delete Econsent form`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "delete", data }))}
          disabled={data.is_published_flag === true}
        >
          <ThemeImage imageName="deleteImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Send E-consent Form`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "econsentStatusChange", data }))}
          disabled={data.is_published_flag === true}
        >
          <ThemeImage imageName="manageResearchImage" />
        </button>
      </Tooltip>
    </div>
  );

  const handleSearchInputChange = (value: string) => {
    setLocalSearchText(value);

    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300); // Adjust the delay as needed (300 ms in this case)

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
    handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: value,
        status,
        perPage,
      },
      navigate,
      location
    );
  };


  const handlePerPageFilter = (value: number) => {
    setLocalPerPage(Number(value));

    handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status: localStatus,
        perPage: Number(value),
      },
      navigate,
      location
    );
  };

  const handleSort = (column: string) => {
    const newDirection = sort === "asc" ? "desc" : "asc";

    handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: page,
        sort: newDirection,
        sortColumn: column,
        searchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePageChange = (selectedItem: { selected: number }) => {
    handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: selectedItem.selected + 1,
        sort,
        sortColumn,
        searchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };




  const renderModalContent = () => {
    switch (modalType) {
      case "view":
        return ( ""
       
        );
     
      default:
        return null;
    }
  };
  return (
    <div className="col-lg-12">
      <div className="card shadow-sm rounded-3">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h4 className="mb-0">
            {/* <FontAwesomeIcon icon={faFileMedical} className="me-2" /> */}
            <ThemeImage
              imageName="manageEconsentImage"
              alt="manageEconsentImage"
              className={
                currentTheme === "dark"
                  ? "dark-icon-image me-2"
                  : "light-icon-image me-2"
              }
            />
            Consent Forms List
          </h4>
          <CommonButton
            type="button"
            className="filled-btn"
            text="Add"
            icon={<FontAwesomeIcon icon={faAdd} />}
            onClick={() => {
                const url = ADD_CONSENT_FORMS_APP_URL;
                navigate(url,
                 );
            }}
          />
        </div>

        <div className="table_top">
          <div className="search_outer">
            <input
              placeholder="Search Econsent data"
              onChange={(e) => handleSearchInputChange(e.target.value)}
              // style={{ width: 300 }}
              value={localSearchText}
            />
            <div className="info-icon">
              <Tooltip
                title={`Search by Consent Title,Created Date`}
              >
                <FontAwesomeIcon icon={faInfoCircle} className="fa-fw" />
              </Tooltip>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
            <CommonDropdown
              label="Status"
              options={[
                { label: "All", value: "all" },
                { label: "Send E-consent", value: "published" },
                { label: "Draft", value: "draft" },
              ]}
              selectedValue={localStatus}
              onSelect={(value) => handleStatusFilter(value as any)}
            />
            <CommonDropdown
              label="Per Page"
              options={[
                { label: "5", value: 5 },
                { label: "10", value: 10 },
                { label: "15", value: 15 },
              ]}
              selectedValue={localPerPage}
              onSelect={(value) => handlePerPageFilter(Number(value))}
            />
          </div>
        </div>

        <div className="card-body">
          {/* <CommonTable
            columns={columns}
            data={formattedRecords}
            sortColumn={sortColumn}
            sortDirection={sort}
            onSort={handleSort}
            onPageChange={handlePageChange}
            totalPages={totalPages}
            currentPage={page}
            renderActions={renderActions}
            totalRecords={totalRecords}
          /> */}
           <PDFViewer pdfUrl={'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'} />
        </div> 
         <CommonModal
          show={isOpen && modalType !== "delete"  && modalType !== "econsentStatusChange"}
          onHide={() => dispatch(closeModal())}
          title={
            modalType === "add"
              ? "Add New Patient"
             
              : modalType === "view"
              ? "View Patient"
            
              : ""
          }
        >
        {renderModalContent()}
        </CommonModal>
        <ConfirmationModal
          show={modalType === "delete" || modalType === "econsentStatusChange"}
          onHide={() => dispatch(closeModal())}
          onConfirm={() => {
            const getData = {
              currentPage: page,
              sort,
              sortColumn,
              searchText,
              status,
              perPage,
            };
            modalType === "delete"
              ?  handleEconsentDeletion(
                  dispatch,
                  selectedData.id,
                  getData,
                  navigate,
                  location,
                  records
                )
              :  
             handleEconsentStatusChange(
              dispatch,
              selectedData.id,
              true,
              getData,
              navigate,
              location,
              
            );

          }}
          title={modalType === "delete" ? "Confirm Delete" : "Confirm Send E-consent"}
          message={
            modalType === "delete"
              ? "Are you sure you want to delete this econsent form?"
              : "Are you sure you want to send this e-consent form? Once sent, you will not be able to edit or delete it."
          }
        />
      </div>
    </div>
  );
};

export default PatientList;
