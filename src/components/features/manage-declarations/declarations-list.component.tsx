import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Column } from "../../../types/table.type";
import moment from "moment";
import DeclarationForm from "./DeclarationForm";
import DeclarationView from "./declaration-view-modal.component"
 import {
    ISDeclarationFormField,
    IDeclarationRecord,
    IFetchDeclarationtDetailsParams,
  } from "../../../types/manage-declaration.type";
 import {
   handleDeclarationCreation,
   handleDeclarationUpdate,
   handleFetchAllDeclarationDetails,
   handleDeclarationDeletion,
   } from "../../../pages/manage-declarations/manage-declarations.event";
import { Tooltip } from "antd";
import ThemeImage from "../../common/icons/ThemeImage";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle,faAdd, faUser } from "@fortawesome/free-solid-svg-icons";
import CommonButton from "../../common/button/CommonButton";
import {openModal, closeModal } from "../../../redux/action";
import CommonModal from "../../common/Modal/CommonModal";
import CommonDropdown from "../../common/dropdown/CommonDropdown";
import CommonTable from "../../common/table/CommonTable";
import ConfirmationModal from "../../common/Modal/ConfirmationModal";



const DeclarationList: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    records,
    page,
    totalPages,
    sort,
    sortColumn,
    searchText,
    perPage,
    totalRecords,
    //status,
  } = useAppSelector((state: RootState) => state.ManageDeclarations);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
 // const [localStatus, setLocalStatus] = useState(status);
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const [localPerPage, setLocalPerPage] = useState(perPage);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  
  const { isOpen, modalType, selectedData } = useAppSelector(
    (state: RootState) => state.modal
  );
  const navigate = useNavigate();

  useEffect(() => {
    
    const searchParams = new URLSearchParams(location.search);

    setLocalSearchText(searchParams.get("searchText") || "");
   
    setLocalPerPage(Number(searchParams.get("perPage")) || 10);

    const initialParams: IFetchDeclarationtDetailsParams = {
      currentPage: Number(searchParams.get("currentPage")) || 1,
      perPage: Number(searchParams.get("perPage")) || 10,
      sort: searchParams.get("sort") || "desc",
      sortColumn: searchParams.get("sortColumn") || "created_at",
      searchText: searchParams.get("searchText") || "",
     // status: searchParams.get("status") || "all",
    };

    handleFetchAllDeclarationDetails(dispatch, initialParams, navigate, location);

    // eslint-disable-next-line
  }, [location.search]);
  
  console.log('records',records);
  const columns: Column[] = [
    { key: "title", label: "Title", sortable: true },
    {
      key: "response_type",
      label: "Declaration Type",
      sortable: true
    },
    { key: "created_at", label: "Created On", sortable: true },
    
  ];
  const formattedRecords =
    records && records.length > 0
      ? records.map((declaration: any) => ({
          ...declaration,
          created_at: moment(declaration.created_at).format("YYYY-MM-DD"),
         
        }))
      : [];

  const renderActions = (data: any) => (
    <div className="actions_wrap">
     
      <Tooltip title={`View Declaration Details`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "view", data }))}
        >
          <ThemeImage imageName="viewImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Edit Declaration Details`}>
        <button
          className="action_btn"
          onClick={() => handleModalOpen("edit", data)}
        >
          <ThemeImage imageName="editImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Delete Declaration`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "delete", data }))}
        >
          <ThemeImage imageName="deleteImage" />
        </button>
      </Tooltip>
    </div>
  );

  const handleSearchInputChange = (value: string) => {
    setLocalSearchText(value);

    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300); // Adjust the delay as needed (300 ms in this case)

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
    handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: value,
       // status,
        perPage,
      },
      navigate,
      location
    );
  };


  const handlePerPageFilter = (value: number) => {
    setLocalPerPage(Number(value));

    handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        //status: localStatus,
        perPage: Number(value),
      },
      navigate,
      location
    );
  };

  const handleSort = (column: string) => {
    const newDirection = sort === "asc" ? "desc" : "asc";

    handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: page,
        sort: newDirection,
        sortColumn: column,
        searchText,
        //status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePageChange = (selectedItem: { selected: number }) => {
    handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: selectedItem.selected + 1,
        sort,
        sortColumn,
        searchText,
       // status,
        perPage,
      },
      navigate,
      location
    );
  };
  const [formState, setFormState] = useState<Record<string, string>>({
    title:"",
    statement: "",
    response_type: "",
  });

  const [errorState, setErrorState] = useState<Record<string, string>>({
    title:"",
    statement: "",
    response_type: "",
  });
  const handleModalOpen = (type: string, data: IDeclarationRecord | null) => {
    if (type === "add") {
      setFormState({
        title: "",
        statement: "",
        response_type: "",
      });
      dispatch(openModal({ type: "add" }));
    } else {
      setFormState({
    
        title: data?.title ? data?.title : "",
        statement: data?.statement ? data?.statement : "",
        response_type: data?.response_type ? data?.response_type : "",
      });
     dispatch(openModal({ type: "edit", data }));
    }
  };
  const declarationFormFields: ISDeclarationFormField[] = [
    {
      name: "title",
      label: "Title",
      type: "text",
      placeholder: "Enter Title",
      required: true,
    //  favImage: faUser,
      maxLength: 50,
      validationRules: {
        type: "titleWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
        name: "response_type",
        label: "Declaration Type",
        type: "select",
        placeholder: "Select Declaration Type",
        required: true,
        validationRules: {
          type: "select",
          required: true,
        },
        options: [
          { label: "Text", value: "text" },
          { label: "Yes/No", value: "yes_no" },
          
        ],
        colProps: { xs: 12, md: 6 },
      },
    {
        name: "statement",
        label: "Declaration Statement",
        type: "textarea",
        placeholder: "Declaration Statement",
        required: true,
        favImage: faUser,
        maxLength: 500,
        validationRules: {
          type: "paragraphText",
          maxLength: 500,
          minLength: 3,
          required: true,
        },
        colProps: { xs: 12, md: 12 },
      },

   
  ];
  const handleDeclarationCancel = () => {
    dispatch(closeModal());
    setFormState({
        title: "",
        statement: "",
        response_type: "",
    
    });
    setErrorState({
        title: "",
        statement: "",
        response_type: "",
      
    });
  };
  const isFormValid = () => {
    const isAnyFieldEmpty = declarationFormFields.some(
      (field: any) => field.required && !formState[field.name]
    );
    const hasErrors = Object.values(errorState).some(
      (error) => error && error.length > 0
    );
    return !isAnyFieldEmpty && !hasErrors;
  };
  const renderModalContent = () => {
    switch (modalType) {
      case "add":
      case "edit":
        return (
          <DeclarationForm
            formState={formState}
            setFormState={setFormState}
            errorState={errorState}
            setErrorState={setErrorState}
            onSubmit={() => {
                const getData = {
                  currentPage: page,
                  sort,
                  sortColumn,
                  searchText,
                 // status,
                  perPage,
                };
                modalType === "edit"
                ? handleDeclarationUpdate(
                    dispatch,
                    formState,
                    declarationFormFields,
                    selectedData.id,
                    setErrorState,
                    getData,
                    navigate,
                    location
                  )
                :  
                 handleDeclarationCreation(
                    dispatch,
                    formState,
                    declarationFormFields,
                    setErrorState,
                    null,
                    navigate,
                    location
                )
               
               
            }}
            isFormValid={isFormValid}
            fields={declarationFormFields}
            isEditModal={modalType === "edit" ? true : false}
            dispatch={dispatch}
            onCancel={handleDeclarationCancel}
          />
        );
      case "view":
        return (
          <DeclarationView
            selectedData={selectedData}
            onDelete={() =>
              dispatch(openModal({ type: "delete", data: selectedData }))
            }
            onEdit={() => handleModalOpen("edit", selectedData)}
            onClose={() => dispatch(closeModal())}
          />
        );
     
      default:
        return null;
    }
  };
  return (
    <div className="col-lg-12">
      <div className="card shadow-sm rounded-3">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h4 className="mb-0">
            {/* <FontAwesomeIcon icon={faFileMedical} className="me-2" /> */}
            <ThemeImage
              imageName="manageDeclarationImage"
              alt="manageDeclarationImage"
              className={
                currentTheme === "dark"
                  ? "dark-icon-image me-2"
                  : "light-icon-image me-2"
              }
            />
            Declaration List
          </h4>
          <CommonButton
            type="button"
            className="filled-btn"
            text="Add"
            icon={<FontAwesomeIcon icon={faAdd} />}
            onClick={() => handleModalOpen("add", null)}
          />
        </div>

        <div className="table_top">
          <div className="search_outer">
            <input
              placeholder="Search Declaration"
              onChange={(e) => handleSearchInputChange(e.target.value)}
              // style={{ width: 300 }}
              value={localSearchText}
            />
            <div className="info-icon">
              <Tooltip
                title={`Search by Title,Created Date`}
              >
                <FontAwesomeIcon icon={faInfoCircle} className="fa-fw" />
              </Tooltip>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
         
            <CommonDropdown
              label="Per Page"
              options={[
                { label: "5", value: 5 },
                { label: "10", value: 10 },
                { label: "15", value: 15 },
              ]}
              selectedValue={localPerPage}
              onSelect={(value) => handlePerPageFilter(Number(value))}
            />
          </div>
        </div>

        <div className="card-body">
          <CommonTable
            columns={columns}
            data={formattedRecords}
            sortColumn={sortColumn}
            sortDirection={sort}
            onSort={handleSort}
            onPageChange={handlePageChange}
            totalPages={totalPages}
            currentPage={page}
            renderActions={renderActions}
            totalRecords={totalRecords}
          />
        </div> 
         <CommonModal
          show={isOpen && modalType !== "delete"}
          onHide={() => dispatch(closeModal())}
          title={
            modalType === "add"
              ? "Add New Declaration"
              : modalType === "edit"
              ? "Edit Declaration"
              : modalType === "view"
              ? "View Declaration"
              : ""
          }
        >
        {renderModalContent()}
        </CommonModal>
        <ConfirmationModal
          show={modalType === "delete"}
          onHide={() => dispatch(closeModal())}
          onConfirm={() => {
            const getData = {
              currentPage: page,
              sort,
              sortColumn,
              searchText,
              //status,
              perPage,
            };
            handleDeclarationDeletion(
              dispatch,
              selectedData.id,
              getData,
              navigate,
              location,
              records
            );
          }}
          title="Confirm Delete"
          message="Are you sure you want to delete this statement?"
        />
      </div>
    </div>
  );
};

export default DeclarationList;
