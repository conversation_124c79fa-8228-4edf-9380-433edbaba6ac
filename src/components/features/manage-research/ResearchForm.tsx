import React,  { useEffect, useState } from "react";
import CommonForm from "../../common/form/CommonForm";
import CommonButton from "../../common/button/CommonButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSave,faPlus } from "@fortawesome/free-solid-svg-icons";
import { useAppSelector,useAppDispatch } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import {
  handleAddResearchCategories,
  handleViewResearchDocument
} from "../../../pages/manage-research/manage-research.event";


const ResearchForm: React.FC<any> = ({
  formState,
  setFormState,
  errorState,
  setErrorState,
  onSubmit,
  isFormValid,
  fields,
  isEditModal,
  onCancel,
}) => {
  const loading = useAppSelector((state: RootState) => state.loader.loader);
   const dispatch = useAppDispatch();

   
  const [categoryOptions, setCategoryOptions] = useState(
    fields.find((f) => f.name === "category_id")?.options || []
  );
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  const [newCategory, setNewCategory] = useState("");

  // Inject updated category options back into the form fields
  const updatedFields = fields.map((field) =>
    field.name === "category_id"
      ? { ...field, options: categoryOptions }
      : field
  );

  const handleViewDocument = async () => {
      try {
        const responseData = await handleViewResearchDocument(dispatch, formState.id);
        
        if (!responseData || responseData.status !== 200) {
          throw new Error("Failed to fetch document");
        }
  
        const blob = new Blob([responseData.data], {
          type: responseData?.headers['content-type'] || 'application/pdf',
        });
  
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');
        } catch (error) {
          console.error("Error opening document:", error);
          //alert("Unable to open document.");
        }
  };
  const handleAddCategory = async () => {
    if (newCategory.trim() === "") return;
  
    try {
      const responseData = await handleAddResearchCategories(dispatch, {
        name: newCategory,
      });
      
     
      if (!responseData  || responseData.length === 0 || !responseData.name || !responseData.id) {
        throw new Error("Category not saved or invalid response.");
      }
        
     
      const option = {
        label: responseData.name,
        value: responseData.id,
      };

  
      setCategoryOptions((prev) => [...prev, option]);
      setFormState({ ...formState, category_id: option.value });
      setNewCategory("");
      setShowNewCategoryInput(false);
      setErrorState({}); // clear any previous errors
    } catch (error) {
      console.error("Error adding category:", error);
      
    }
  };
  
  

  return (
    <>
      <CommonForm
        state={formState}
        setState={setFormState}
        errorState={errorState}
        setErrorState={setErrorState}
        fields={updatedFields}
        onSubmit={onSubmit}
      />

      <div className="mt-3">
        {!showNewCategoryInput ? (
          <button
            type="button"
            className="btn btn-sm btn-outline-primary"
            onClick={() => setShowNewCategoryInput(true)}
          >
            <FontAwesomeIcon icon={faPlus} /> Add New Category
          </button>
        ) : (
          <div className="d-flex gap-2 align-items-center mt-2">
            <input
              type="text"
              className="form-control"
              placeholder="Enter new category"
              value={newCategory}
              onChange={(e) => setNewCategory(e.target.value)}
            />
            <button
              type="button"
              className="btn btn-sm btn-success"
              onClick={handleAddCategory}
            >
              Add
            </button>
            <button
              type="button"
              className="btn btn-sm btn-secondary"
              onClick={() => {
                setNewCategory("");
                setShowNewCategoryInput(false);
              }}
            >
              Cancel
            </button>
          </div>
        )}
      </div>
      {isEditModal && formState.research_document && (
        <div className="mt-3">
          <strong>Previously Uploaded Document:</strong>{" "}
         
          <button
            className="btn btn-link p-0"
            onClick={handleViewDocument}
            type="button"
          >
            View Document
          </button>
        </div>
      )}
      <div className="d-flex justify-content-end mt-3 gap-2">
        <CommonButton
          type="button"
          className="outlined-btn"
          text="Cancel"
          onClick={onCancel}
        />
        <CommonButton
          type="submit"
          className={!isFormValid() ? "disabled-btn" : "filled-btn"}
          text={isEditModal ? "Update" : "Submit"}
          icon={<FontAwesomeIcon icon={faSave} />}
          onClick={onSubmit}
          loading={loading}
          disabled={!isFormValid()}
        />
      </div>
    </>
  );
};


export default ResearchForm;
