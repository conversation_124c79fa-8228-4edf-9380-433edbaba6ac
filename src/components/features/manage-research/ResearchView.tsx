import React from "react";
import { <PERSON><PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faEdit } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import { IResearchViewProps } from "../../../types/manage-research.type";
import { useAppDispatch } from "../../../redux/hooks";
import {
  handleViewResearchDocument
} from "../../../pages/manage-research/manage-research.event";

const ResearchView: React.FC<IResearchViewProps> = ({
  selectedData,
  onDelete,
  onEdit,
  onClose,
}) => {
  const dispatch = useAppDispatch();
  const handleViewDocument = async () => {
    try {
      const responseData = await handleViewResearchDocument(dispatch, selectedData.id);
      
      if (!responseData || responseData.status !== 200) {
        throw new Error("Failed to fetch document");
      }

      const blob = new Blob([responseData.data], {
        type: responseData?.headers['content-type'] || 'application/pdf',
      });

      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
      } catch (error) {
        console.error("Error opening document:", error);
        //alert("Unable to open document.");
      }
  };
  return (
    <div>
      <div className="px-0 report_detail container">
        <div className="row row-gap-3">
          {[
            { label: "Title", value: selectedData.title },
            { label: "Category", value: selectedData.category_name },
            {
              label: "Document",
              value: selectedData.document?.file_path ? (
                <button
                  className="btn btn-link p-0"
                  onClick={handleViewDocument}
                  type="button"
                >
                  View Document
                </button>
              ) : (
                "No Document"
              ),
            },
            {
              label: "Created On",
              value: moment(selectedData.created_at).format("YYYY-MM-DD"),
            },
            
          ].map((detail, index) => (
            <div key={index} className={detail.label === "Declaration Statement" ? "col-md-12" : "col-md-6"}>
              <div className="report_inn">
                <p className="mb-1 form-label">{detail.label}</p>
                <p className="txt">{detail.value}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="d-flex justify-content-between w-100 btn_wrap mt-3">
        {selectedData.is_published_flag !== true && (
          <Button
          variant="outline-danger"
          className="danger-btn"
          onClick={onDelete}
           >
          <FontAwesomeIcon icon={faTrash} /> Delete 
        </Button>
        )}
        <div className="btn_grp">
          {selectedData.is_published_flag !== true && (
            <Button variant="primary" className="filled-btn" onClick={onEdit} >
              <FontAwesomeIcon icon={faEdit} /> Edit
            </Button>
          )}
          <Button className="outlined-btn" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ResearchView;
