import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Column } from "../../../types/table.type";
import moment from "moment";
import ResearchForm from "./ResearchForm";
import ResearchView from "./ResearchView"
import {
    IFetchResearchDetailsParams,
    ISResearchFormField,
    IResearchRecord,
  } from "../../../types/manage-research.type";
import {
    handleFetchAllResearchCategories,
    handleFetchAllResearchDetails,
    handleResearchCreation,
    handleResearchDeletion,
    handleResearchStatusChange,
    handleResearchUpdate,
  } from "../../../pages/manage-research/manage-research.event";
import { Toolt<PERSON>,Switch } from "antd";
import { MANAGE_RESEARCH_APP_URL } from "../../../constants/appUrl";
import ThemeImage from "../../common/icons/ThemeImage";


import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle,faAdd, faUser } from "@fortawesome/free-solid-svg-icons";
import CommonButton from "../../common/button/CommonButton";
import {openModal, closeModal } from "../../../redux/action";
import CommonModal from "../../common/Modal/CommonModal";
import CommonDropdown from "../../common/dropdown/CommonDropdown";
import CommonTable from "../../common/table/CommonTable";
import ConfirmationModal from "../../common/Modal/ConfirmationModal";

const ResearchList: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    records,
    page,
    totalPages,
    sort,
    sortColumn,
    searchText,
    perPage,
    totalRecords,
    status,
  } = useAppSelector((state: RootState) => state.manageResearch);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  const [localStatus, setLocalStatus] = useState(status);
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const [localPerPage, setLocalPerPage] = useState(perPage);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [categoryOptions, setCategoryOptions] = useState([]);

  const { isOpen, modalType, selectedData } = useAppSelector(
    (state: RootState) => state.modal
  );
  const navigate = useNavigate();
  
  useEffect(() => {
    const fetchCategories = async () => {
      const all_categories = await handleFetchAllResearchCategories(dispatch);

      if (Array.isArray(all_categories)) {
        const formattedOptions = all_categories.map((cat: any) => ({
          label: cat.name,
          value: cat.id,
        }));
        setCategoryOptions(formattedOptions);
      }
    };

    fetchCategories();
  }, [dispatch]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);

    setLocalSearchText(searchParams.get("searchText") || "");

    setLocalPerPage(Number(searchParams.get("perPage")) || 10);
    const statusParam = searchParams.get("status");
    if (
      statusParam === "all" ||
      statusParam === "draft" ||
      statusParam === "published"
    ) {
      setLocalStatus(statusParam);
    } else {
      setLocalStatus("all");
    }

    const initialParams: IFetchResearchDetailsParams = {
      currentPage: Number(searchParams.get("currentPage")) || 1,
      perPage: Number(searchParams.get("perPage")) || 10,
      sort: searchParams.get("sort") || "desc",
      sortColumn: searchParams.get("sortColumn") || "created_at",
      searchText: searchParams.get("searchText") || "",
      status: searchParams.get("status") || "all",
    };

    handleFetchAllResearchDetails(dispatch, initialParams, navigate, location);



    // eslint-disable-next-line
  }, [location.search]);

  const handleStatusFilter = (value: "all" | "draft" | "published") => {
      setLocalStatus(value); // now `value` is correctly typed
  
      handleFetchAllResearchDetails(
        dispatch,
        {
          currentPage: 1,
          sort,
          sortColumn,
          searchText: localSearchText,
          status: value,
          perPage,
        },
        navigate,
        location
      );
    };

 


  const columns: Column[] = [
    { key: "title", label: "Research Title", sortable: true },
    // {
    //   key: "research_doc",
    //   label: "Research Document",
    //   sortable: true,
    //   render: (value: any, item: any) => (
    //     <span
    //       className="view-modal-open"
    //       onClick={() => {
    //         const url = MANAGE_RESEARCH_APP_URL.replace(":researchID", item.id);
    //         navigate(url, {
    //           state: { data: item.id },
    //         });
    //       }}
    //     >
    //       {value}
    //     </span>
    //   ),
    // },
    { key: "category_name", label: "Category", sortable: true },
    { key: "created_at", label: "Created On", sortable: true },
    {
      key: "is_published",
      label: "Status",
      sortable: true,
      
     
    },
  ];

  const formattedRecords =
  records && records.length > 0
    ? records.map((research: any) => {
        const isPublished =
          research.is_published === true ||
          research.is_published === "true" ||
          research.is_published === 1;

        return {
          ...research,
          is_published_flag: isPublished,
          created_at: moment(research.created_at).format("YYYY-MM-DD"),
          is_published: (
            <span className={`custom-badge badge ${isPublished ? "published" : "draft"}`}>
              {isPublished ? "Published" : "Draft"}
            </span>
          ),
        };
      })
    : [];
  const renderActions = (data: any) => (
    <div className="actions_wrap">
       <Tooltip title={`View Research Details`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "view", data }))}
        >
          <ThemeImage imageName="viewImage" />
        </button>
      </Tooltip>
      
      <Tooltip title={`Edit Research Details`}>
        <button
          className="action_btn"
          onClick={() => handleModalOpen("edit", data)}
          disabled={data.is_published_flag === true}
        >
          <ThemeImage imageName="editImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Delete Research`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "delete", data }))}
          disabled={data.is_published_flag === true}
        >
          <ThemeImage imageName="deleteImage" />
        </button>
      </Tooltip>
      <Tooltip title={`Change Research Status`}>
        <button
          className="action_btn"
          onClick={() => dispatch(openModal({ type: "researchStatusChange", data }))}
          disabled={data.is_published_flag === true}
        >
          <ThemeImage imageName="manageResearchImage" />
        </button>
      </Tooltip>
     
    </div>
  );

  const handleSort = (column: string) => {
    const newDirection = sort === "asc" ? "desc" : "asc";
    handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: page,
        sort: newDirection,
        sortColumn: column,
        searchText: localSearchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePageChange = (selectedItem: { selected: number }) => {
    handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: selectedItem.selected + 1,
        sort,
        sortColumn,
        searchText: localSearchText,
        status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handleSearchInputChange = (value: string) => {
    setLocalSearchText(value);

    // Clear the previous timeout if it exists
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout
    const newTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300); // Adjust the delay as needed (300 ms in this case)

    setSearchTimeout(newTimeout);
  };

  const handleSearch = (value: string) => {
    handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: 1,
        sort,
        sortColumn,
        searchText: value,
        status,
        perPage,
      },
      navigate,
      location
    );
  };

  const handlePerPageFilter = (value: number) => {
    setLocalPerPage(Number(value));
    handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: page,
        sort,
        sortColumn,
        searchText: localSearchText,
           status: localStatus,
        perPage: Number(value),
      },
      navigate,
      location
    );
  };
  const [formState, setFormState] = useState<Record<string, string>>({
      id:"",
      title: "",
      document: "",
      category_id:"",
      research_category: "",
  });

  const [errorState, setErrorState] = useState<Record<string, string>>({

      title: "",
      document: "",
      category_id:"",
      research_category: "",
  });
  const handleModalOpen = (type: string, data: IResearchRecord | null) => {
    if (type === "add") {
      setFormState({
        title: "",
        document: "",
        category_id:"",
        research_category: "",
      });
      dispatch(openModal({ type: "add" }));
    } else {
      setFormState({
        id: data?.id ? data?.id : "",
        title: data?.title ? data?.title : "",
        document: data?.document ? data?.document?.file_path : "",
        category_id:data?.category_id ? data?.category_id : "",
        research_document: data.document?.file_path ? data?.document?.file_path : "",
      });
      dispatch(openModal({ type: "edit", data }));
    }
  };
  const researchFormFields: ISResearchFormField[] = [
    {
      name: "title",
      label: "Research Title",
      type: "text",
      placeholder: "Enter Research Title",
      required: true,
      favImage: faUser,
      maxLength: 50,
      validationRules: {
        type: "titleWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "document",
      label: "Upload Document",
      type: "file",
      placeholder: "Upload research document",
      required: true,
      favImage: faUser,
      accept :".doc,.docx,.pdf",
      validationRules: {
        type: "file",
        required: true,
        maxSize: 104857600
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "category_id",
      label: "Category ",
      type: "select",
      placeholder: "Enter Research Title",
      required: true,
      validationRules: {
        type: "select",
        required: true,
      },
     
      options: categoryOptions,
      colProps: { xs: 12, md: 6 },
    },


  ];
  const researchFormFieldsEdit: ISResearchFormField[] = [
    {
      name: "title",
      label: "Research Title",
      type: "text",
      placeholder: "Enter Research Title",
      required: true,
      favImage: faUser,
      maxLength: 50,
      validationRules: {
        type: "titleWithSpace",
        maxLength: 50,
        minLength: 3,
        required: true,
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "document",
      label: "Upload Document",
      type: "file",
      placeholder: "Upload research document",
      required: false,
      favImage: faUser,
      accept :".doc,.docx,.pdf",
      validationRules: {
        type: "file",
        required: true,
        maxSize: 104857600
      },
      colProps: { xs: 12, md: 6 },
    },
    {
      name: "category_id",
      label: "Category ",
      type: "select",
      placeholder: "Enter Research Title",
      required: true,
      validationRules: {
        type: "select",
        required: true,
      },
    
      options: categoryOptions,
      colProps: { xs: 12, md: 6 },
    },



  ];
  const handleResearchCancel = () => {
    dispatch(closeModal());
    setFormState({
      title: "",
      document: "",
      category_id:"",
      research_category: "",

    });
    setErrorState({
      title: "",
      document: "",
      category_id:"",
      research_category: "",

    });
  };
  const isFormValid = () => {
    const isAnyFieldEmpty = researchFormFields.some(
      (field: any) => field.required && !formState[field.name]
    );
    const hasErrors = Object.values(errorState).some(
      (error) => error && error.length > 0
    );
    return !isAnyFieldEmpty && !hasErrors;
  };
  const isFormValidEdit = () => {
    const isAnyFieldEmpty = researchFormFieldsEdit.some(
      (field: any) => field.required && !formState[field.name]
    );
    const hasErrors = Object.values(errorState).some(
      (error) => error && error.length > 0
    );
    return !isAnyFieldEmpty && !hasErrors;
  };
  const renderModalContent = () => {
    switch (modalType) {
      case "add":
        return (
          <ResearchForm
            formState={formState}
            setFormState={setFormState}
            errorState={errorState}
            setErrorState={setErrorState}
            onSubmit={() => {
              const getData = {
                currentPage: page,
                sort,
                sortColumn,
                searchText,
             //   status,
                perPage,
              };
               handleResearchCreation(
                    dispatch,
                    formState,
                    researchFormFields,
                    setErrorState,
                    getData,
                    navigate,
                    location
                  );
            }}
            isFormValid={isFormValid}
            fields={researchFormFields}
            dispatch={dispatch}
            onCancel={handleResearchCancel}
          />
        );
        case "edit":
          return (
            <ResearchForm
              formState={formState}
              setFormState={setFormState}
              errorState={errorState}
              setErrorState={setErrorState}
              onSubmit={() => {
                const getData = {
                  currentPage: page,
                  sort,
                  sortColumn,
                  searchText,
                  status,
                  perPage,
                };
                handleResearchUpdate(
                      dispatch,
                      formState,
                      researchFormFields,
                      selectedData.id,
                      setErrorState,
                      getData,
                      navigate,
                      location
                    )

              }}
              isFormValid={isFormValidEdit}
              fields={researchFormFieldsEdit}
              dispatch={dispatch}
              isEditModal={true}
              onCancel={handleResearchCancel}
            />
      );
      case "view":
        return (
          <ResearchView
            selectedData={selectedData}
            onDelete={() =>
              dispatch(openModal({ type: "delete", data: selectedData }))
            }
            onEdit={() => handleModalOpen("edit", selectedData)}
            onClose={() => dispatch(closeModal())}
          />
        );
        

      default:
        return null;
    }
  };
  return (
    <div className="col-lg-12">
      <div className="card shadow-sm rounded-3">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h4 className="mb-0">
            {/* <FontAwesomeIcon icon={faFileMedical} className="me-2" /> */}
            <ThemeImage
              imageName="manageResearchImage"
              alt="manageResearchImage"
              className={
                currentTheme === "dark"
                  ? "dark-icon-image me-2"
                  : "light-icon-image me-2"
              }
            />
            Research List
          </h4>
          <CommonButton
            type="button"
            className="filled-btn"
            text="Add"
            icon={<FontAwesomeIcon icon={faAdd} />}
            onClick={() => handleModalOpen("add", null)}
          />
        </div>

        <div className="table_top">
          <div className="search_outer">
            <input
              placeholder="Search Research"
              onChange={(e) => handleSearchInputChange(e.target.value)}
              // style={{ width: 300 }}
              value={localSearchText}
            />
            <div className="info-icon">
              <Tooltip
                title={`Search by Title,Created Date`}
              >
                <FontAwesomeIcon icon={faInfoCircle} className="fa-fw" />
              </Tooltip>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
          <CommonDropdown
              label="Status"
              options={[
                { label: "All", value: "all" },
                { label: "Published", value: "published" },
                { label: "Draft", value: "draft" },
              ]}
              selectedValue={localStatus}
              onSelect={(value) => handleStatusFilter(value as any)}
            />
            <CommonDropdown
              label="Per Page"
              options={[
                { label: "5", value: 5 },
                { label: "10", value: 10 },
                { label: "15", value: 15 },
              ]}
              selectedValue={localPerPage}
              onSelect={(value) => handlePerPageFilter(Number(value))}
            />
          </div>
        </div>

        <div className="card-body">
          <CommonTable
            columns={columns}
            data={formattedRecords}
            sortColumn={sortColumn}
            sortDirection={sort}
            onSort={handleSort}
            onPageChange={handlePageChange}
            totalPages={totalPages}
            currentPage={page}
            renderActions={renderActions}
            totalRecords={totalRecords}
          />
        </div>
         <CommonModal
          show={isOpen && modalType !== "delete" && modalType !== "researchStatusChange" }
          onHide={() => dispatch(closeModal())}
          title={
            modalType === "add"
              ? "Add New Research"
              : modalType === "edit"
              ? "Edit Research"
              : modalType === "view"
              ? "View Research"

              : ""
          }
        >
        {renderModalContent()}
        </CommonModal>
        <ConfirmationModal
          show={modalType === "delete" || modalType === "researchStatusChange"}
          onHide={() => dispatch(closeModal())}
          onConfirm={() => {
            const getData = {
              currentPage: page,
              sort,
              sortColumn,
              searchText,
              status,
              perPage,
            };
            modalType === "delete"
              ?  handleResearchDeletion(
                  dispatch,
                  selectedData.id,
                  getData,
                  navigate,
                  location,
                  records
                )
              :  
             handleResearchStatusChange(
              dispatch,
              selectedData.id,
              true,
              getData,
              navigate,
              location,
              
            );
            
          }}
          title={modalType === "delete" ? "Confirm Delete" : "Confirm Publish"}
          message={
            modalType === "delete"
              ? "Are you sure you want to delete this research?"
              : "Are you sure you want to publish this research? Once published, you will not be able to edit or delete it."
          }
        />
      </div>
    </div>
  );
};

export default ResearchList;
