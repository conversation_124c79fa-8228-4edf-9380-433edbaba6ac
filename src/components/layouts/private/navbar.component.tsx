import { <PERSON>,useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import { HeaderProps } from "../../../types/layouts.type";
import {
  logoutUser,
  resetState,
  setLoader,
  setTheme,
} from "../../../redux/action";
import { SuccessMessageInterface } from "../../../types/redux.type";
import { clearLocalStorageData } from "../../../utils/storage/localStorageUtils";
import {
  LOGIN_APP_URL,MANAGE_PROFILE_APP_URL
} from "../../../constants/appUrl";
import { useEffect, useState } from "react";
import humburgerIcon from "../../../assets/images/hamburger.svg";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import moonImage from "../../../assets/images/moon.svg";
import sunImage from "../../../assets/images/sun.svg";
import {
  
  faEnvelope,
  faSignOutAlt,
  faUser,
  
} from "@fortawesome/free-solid-svg-icons";


const Header: React.FC<HeaderProps> = ({ toggleSidebar }) => {
  const user = useAppSelector((state: RootState) => state.auth.user);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showMobileNotifications, setShowMobileNotifications] = useState(false);

  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const toggleTheme = async () => {
    const newTheme: any = currentTheme === "dark" ? "light" : "dark";
    await dispatch(setTheme(newTheme));
    document.documentElement.setAttribute("data-bs-theme", newTheme);
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    if (themeColorMeta) {
      themeColorMeta.setAttribute("content", newTheme === "dark" ? "#282828" : "#ffffff");
    }
  };

  const handleNotificationDropdown = () => {
    setShowNotifications(!showNotifications);
  };

  const handleMobileNotificationDropdown = () => {
    setShowMobileNotifications(!showMobileNotifications);
  };

  const handleLogout = async () => {
    try {
      await dispatch(setLoader(true));
      await dispatch(
        logoutUser({}, async (response: SuccessMessageInterface) => {
          await dispatch(setLoader(false));
          if (response.success) {
            await clearLocalStorageData("token");
            navigate(LOGIN_APP_URL);
            await dispatch(resetState());
          }
        })
      );
    } catch (err) {
      console.error("Error fetching website data:", err);
    } finally {
      await dispatch(setLoader(false));
    }
  };

  const handleProfile = () => {
     navigate(MANAGE_PROFILE_APP_URL);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const notificationMenu = document.querySelector(".notification-menu");
      const notificationIcon = document.querySelector(".notification-icon");
      if (
        notificationMenu &&
        !notificationMenu.contains(event.target as Node) &&
        notificationIcon &&
        !notificationIcon.contains(event.target as Node)
      ) {
        setShowNotifications(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      {/* Desktop Header */}
      <nav
        className={`navbar navbar-expand px-3 py-1 border-bottom header desktop-header ${
          currentTheme === "dark" ? "dark" : "light"
        }`}
      >
        <button
          className="btn"
          id="sidebar-toggle"
          type="button"
          onClick={toggleSidebar}
        >
          <img src={humburgerIcon} alt="humburger_icon" />
        </button>
        <div className="navbar-collapse navbar">
          <ul className="navbar-nav ms-auto">
          <li className="nav-item d-flex align-items-center gap-4">
              <button
                className="theme-toggle nav-link p-0"
                onClick={toggleTheme}
              >
                <span className="moon">
                  <img
                    src={currentTheme === "light" ? moonImage : sunImage}
                    alt="moon"
                    width={20}
                  />
                </span>
              </button>
            </li>
            <li className="dropdown profile-dropdown">
              <Link
                to="#"
                className="btn btn-secondary dropdown-toggle px-0"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span className="profile-icn">
                  {user?.first_name
                    ? user?.first_name[0].toUpperCase()
                    : user?.email[0].toUpperCase()}
                </span>
              </Link>
              <ul className="dropdown-menu dropdown-menu-end">
                <li>
                  <button className="dropdown-item border-bottom mb-2 pb-2">
                    <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                    {user?.email}
                  </button>
                </li>
                <li>
                  <button className="dropdown-item" onClick={handleProfile}>
                    <FontAwesomeIcon icon={faUser} className="me-2" />
                    Profile
                  </button>
                </li>
                <li>
                  <button className="dropdown-item" onClick={handleLogout}>
                    <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
                    Logout
                  </button>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </nav>

      {/* Mobile Header */}
      <nav
        className={`navbar navbar-expand header  mobile-header flex-column ${
          currentTheme === "dark" ? "dark" : "light"
        }`}
      >

      </nav>
    </>
  );
};

export default Header;
