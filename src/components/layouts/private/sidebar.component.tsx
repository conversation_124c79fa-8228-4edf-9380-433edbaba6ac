// src/components/Sidebar/Sidebar.tsx

import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faChevronDown,
  faChevronUp,
  faQuestionCircle,
  //faHourglassHalf,
  faTimes,
} from "@fortawesome/free-solid-svg-icons";
import logo_light from "../../../assets/images/logo_new.jpeg";
import logo_dark from "../../../assets/images/logo_new-dark.png";
import {
  SidebarItem,
  SidebarProps,
  SidebarSubItem,
} from "../../../types/layouts.type";
import { useAppSelector } from "../../../redux/hooks";
import { RootState } from "../../../redux/store";
import ThemeImage from "../../common/icons/ThemeImage";
import { DASHBOARD_APP_URL,
   MANAGE_RESEARCH_APP_URL ,
   MANAGE_PATIENT_APP_URL,
   MANAGE_VIDEO_APP_URL,
   MANAGE_DECLARATION_APP_URL,
   MANAGE_CONSENT_FORMS_APP_URL,
   MANAGE_PATIENT_CONSENT_APP_URL
   

} from "../../../constants/appUrl";

// Sidebar configuration array with flexible sub-items structure
const sidebarItems: SidebarItem[] = [
  {
    path: DASHBOARD_APP_URL,
    name: "Dashboard",
    imageName: "manageDashboardImage",
    roles: [ "super-admin","admin","patient"],
  },
  // {
  //   path: MANAGE_PATIENT_APP_URL,
  //   name: "Manage Patient",
  //   imageName: "managePatientImage",
  //   roles: ["super-admin", "admin"],
  // },
  {
    path: MANAGE_PATIENT_APP_URL,
    name: "Manage User",
    imageName: "managePatientImage",
    roles: ["super-admin", "admin"],
    subItems: [
      {
        path: MANAGE_PATIENT_APP_URL,
        name: "All Patients",
        imageName: "managePatientImage",
        roles: ["super-admin", "admin"],
        
      },
    ],
  },
  
  // {
  //   path: MANAGE_RESEARCH_APP_URL,
  //   name: "Manage Research Content",
  //   imageName: "manageResearchImage",
  //   roles: [ "super-admin","admin"],
  // },
  {
    path: MANAGE_RESEARCH_APP_URL,
    name: "Manage Research Content",
    imageName: "manageResearchImage",
    roles: [ "super-admin","admin"],
    subItems: [
      {
        path: MANAGE_VIDEO_APP_URL,
        name: "Manage Videos",
        imageName: "manageVideosImage",
        roles: ["super-admin", "admin"],
      },
      {
        path: MANAGE_RESEARCH_APP_URL,
        name: "Research Program",
        imageName: "manageResearchImage",
        roles: [ "super-admin","admin"],
      }
    ],
  },
  // {
  //   path: MANAGE_CONSENT_FORMS_APP_URL,
  //   name: "Manage E-Consent",
  //   imageName: "manageEconsentImage",
  //   roles: [ "super-admin","admin"],
  // },
  {
    path: MANAGE_CONSENT_FORMS_APP_URL,
    name: "Manage E-Consent",
    imageName: "manageEconsentImage",
    roles: [ "super-admin","admin"],
    subItems: [
      {
        path: MANAGE_DECLARATION_APP_URL,
        name: "Manage Declarations",
        imageName: "manageDeclarationImage",
        roles: [ "super-admin","admin"],
      },
        {
          path: MANAGE_CONSENT_FORMS_APP_URL,
          name: "E-Consent",
          imageName: "manageEconsentImage",
          roles: [ "super-admin","admin"],
        },
      {
        path: MANAGE_PATIENT_CONSENT_APP_URL,
        name: "Patient Consents",
        imageName: "managePatientImage",
        roles: ["super-admin", "admin"],
       // icon: faBoxOpen,
      },
    ],
  },
  
    
];

const hasAccess = (userRole: string, itemRoles: string[]): boolean => {
  return itemRoles.includes(userRole);
};

const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  setSidebarCollapsed,
}) => {
  // const dispatch = useAppDispatch();
  // const navigate = useNavigate();
  const location = useLocation();
  const user = useAppSelector((state: RootState) => state.auth.user);
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);

  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>(
    {}
  );

  const toggleDropdown = (itemName: string) => {
    setOpenDropdowns((prevState) => ({
      ...prevState,
      [itemName]: !prevState[itemName],
    }));
  };

  const isActiveRoute = (
    path: string,
    queryParams?: SidebarSubItem["queryParams"]
  ): boolean => {
    if (!queryParams) {
      return location.pathname === path;
    }

    const searchParams = new URLSearchParams(location.search);
    return (
      location.pathname === path &&
      (searchParams.get("orderStatus") === queryParams.orderStatus ||
        searchParams.get("status") === queryParams.status)
    );
  };

  const renderIcon = (item: SidebarItem | SidebarSubItem) => {
    if (item.imageName) {
      return (
        <ThemeImage
          imageName={item.imageName}
          alt={item.name}
          className={
            currentTheme === "dark" ? "dark-icon-image" : "light-icon-image"
          }
        />
      );
    }
    if (item.icon) {
      return <FontAwesomeIcon icon={item.icon} />;
    }
    return null;
  };

  const constructUrl = (subItem: SidebarSubItem): string => {
    if (!subItem.queryParams) return subItem.path;

    const searchParams = new URLSearchParams();
    Object.entries(subItem.queryParams).forEach(([key, value]) => {
      searchParams.append(key, String(value));
    });
    searchParams.append("currentPage", "1");
    searchParams.append("searchText", "");

    return `${subItem.path}?${searchParams.toString()}`;
  };

  const renderSubItems = (subItems: SidebarSubItem[]) => (
    <ul className="sidebar-dropdown list-unstyled">
      {subItems.map((subItem) => (
        <li
          key={`${subItem.path}-${subItem.name}`}
          className={`sidebar-item mb-2 ${
            isActiveRoute(subItem.path, subItem.queryParams) ? "active" : ""
          }`}
        >
          <Link
            to={constructUrl(subItem)}
            className="sidebar-link"
            onClick={() => setSidebarCollapsed(false)}
          >
            <span className="side-ico">{renderIcon(subItem)}</span>
            {subItem.name}
          </Link>
        </li>
      ))}
    </ul>
  );
  return (
    <>
      {collapsed && (
        <div className="overlay" onClick={() => setSidebarCollapsed(false)} />
      )}
      <aside
        id="sidebar"
        className={`flex-column sidebar ${currentTheme} ${
          collapsed ? "collapsed open" : ""
        }`}
      >
        <div className="sidebar-outer h-100">
          <div>
            <div className="logo-box">
              <Link to={DASHBOARD_APP_URL} className="logo-brand">
                <img
                  src={currentTheme === "light" ? logo_light : logo_dark}
                  alt="logo"
                />
              </Link>
              <button
                className="btn ms-auto border-0"
                onClick={() => setSidebarCollapsed(false)}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
              <div className="sidebar-title">E-consent Portal</div>
            <ul className="sidebar-nav" data-bs-parent="#sidebar">
              {sidebarItems.map((item) => {
                  if (hasAccess(user?.role?.name, item.roles)) {
                    const isDropdownOpen = openDropdowns[item.name] || false;

                    return (
                      <li
                        key={item.path}
                        className={`sidebar-item ${
                          !item.subItems && isActiveRoute(item.path)
                            ? "active"
                            : "dropdown-outer"
                        }`}
                      >
                        {item.subItems ? (
                          <>
                            <div
                              className="sidebar-link d-flex gap-2 align-items-center justify-content-between cursor-pointer user-select-none sidebar-list"
                              onClick={() => toggleDropdown(item.name)}
                            >
                              <span className="d-flex gap-2 align-items-center sub-menu-font cursor-pointer">
                                <span className="side-ico cursor-pointer">
                                  {renderIcon(item)}
                                </span>
                                {item.name}
                              </span>
                              <FontAwesomeIcon
                                icon={
                                  isDropdownOpen ? faChevronUp : faChevronDown
                                }
                                className="dropdown-arrow"
                              />
                            </div>
                            {isDropdownOpen && renderSubItems(item.subItems)}
                          </>
                        ) : (
                          <Link
                            to={item.path}
                            className="sidebar-link"
                            onClick={() => setSidebarCollapsed(false)}
                          >
                            <span className="side-ico">{renderIcon(item)}</span>
                            {item.name}
                          </Link>
                        )}
                      </li>
                    );
                  }
                  return null;
              })}
            </ul>
          </div>

          <ul className="sidebar-nav sidebar-btm">
            <li className="sidebar-item">
              <Link
                to="https://www.theranostics.co.nz/faqs/"
                target="_blank"
                className="sidebar-link"
              >
                <span className="side-ico">
                  <FontAwesomeIcon icon={faQuestionCircle} />
                </span>
                Have a Question? Find it here
              </Link>
            </li>
          </ul>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
