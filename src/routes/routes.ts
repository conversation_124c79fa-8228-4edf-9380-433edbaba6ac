import { RouteConfig } from "../types/route.type";
import {
  FORGET_PASSWORD_APP_URL,
  LOGIN_APP_URL,
  REGISTER_APP_URL,
  DASHBOARD_APP_URL,
  <PERSON>NA<PERSON>_PROFILE_APP_URL,
  <PERSON><PERSON><PERSON>_RESEARCH_APP_URL,
  MA<PERSON><PERSON>_PATIENT_APP_URL,
  MANAGE_VIDEO_APP_URL,
  MANAGE_DECLARATION_APP_URL,
  <PERSON><PERSON><PERSON>_CONSENT_FORMS_APP_URL,
  ADD_CONSENT_FORMS_APP_URL,
  EDIT_CONSENT_FORMS_APP_URL,
  <PERSON><PERSON><PERSON>_PATIENT_CONSENT_APP_URL
} from "../constants/appUrl";
import {
  FORGET_PASSWORD_APP_COMPONENT,
  LOGIN_APP_COMPONENT,
  REGISTER_APP_COMPONENT,
  DASHBOARD_APP_COMPONENT,
  <PERSON>NA<PERSON>_PROFILE_APP_COMPONENT,
  <PERSON>NA<PERSON>_RESEARCH_APP_COMPONENT,
  <PERSON><PERSON><PERSON>_PATIENT_APP_COMPONENT,
  <PERSON>NA<PERSON>_VIDEO_APP_COMPONENT,
  MANA<PERSON>_DECLARATION_APP_COMPONENT,
  MANAGE_CONSENT_FORMS_APP_COMPONENT,
  ADD_CONSENT_FORMS_APP_COMPONENT,
  EDIT_CONSENT_FORMS_APP_COMPONENT,
  MANAGE_PATIENT_CONSENT_APP_COMPONENT

} from "../constants/componentUrls";

export const publicRoutes: RouteConfig[] = [
  {
    path: LOGIN_APP_URL,
    component: LOGIN_APP_COMPONENT,
    exact: true,
  },
  {
    path: FORGET_PASSWORD_APP_URL,
    component: FORGET_PASSWORD_APP_COMPONENT,
    exact: true,
  },
  {
    path: REGISTER_APP_URL,
    component: REGISTER_APP_COMPONENT,
    exact: true,
  }
];
export const privateRoutes: RouteConfig[] = [
  {
    path: MANAGE_PROFILE_APP_URL,
    component: MANAGE_PROFILE_APP_COMPONENT,
    exact: true,
    roles: ["super-admin","admin", "patient"],
  },
  {
    path: DASHBOARD_APP_URL,
    component: DASHBOARD_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin", "patient"],
  },
  {
    path: MANAGE_RESEARCH_APP_URL,
    component: MANAGE_RESEARCH_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin"],
  },
  {
    path: MANAGE_PATIENT_APP_URL,
    component: MANAGE_PATIENT_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin"],
  },
  {
    path: MANAGE_PATIENT_CONSENT_APP_URL,
    component: MANAGE_PATIENT_CONSENT_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin"],
  },
  {
    path: MANAGE_VIDEO_APP_URL,
    component: MANAGE_VIDEO_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin"],
  },
  {
    path: MANAGE_DECLARATION_APP_URL,
    component: MANAGE_DECLARATION_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin"],
  },
  {
    path: MANAGE_CONSENT_FORMS_APP_URL,
    component: MANAGE_CONSENT_FORMS_APP_COMPONENT,
    exact: true,
    roles: [ "super-admin","admin"],
  },
  {
    path: ADD_CONSENT_FORMS_APP_URL,
    component: ADD_CONSENT_FORMS_APP_COMPONENT,
    exact: true,
    roles: ["super-admin", "admin"],
  },
  {
    path: EDIT_CONSENT_FORMS_APP_URL,
    component: EDIT_CONSENT_FORMS_APP_COMPONENT,
    exact: true,
    roles: ["super-admin", "admin"],
  },
];

