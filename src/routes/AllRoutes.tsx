import { Suspense, useEffect } from "react";
import { useAppSelector } from "../redux/hooks";
import { RootState } from "../redux/store";
import Loader from "../components/common/loader/Loader";
import { Route, Routes } from "react-router-dom";
import {
  publicRoutes,
  privateRoutes,
} from "./routes";
import {
  PublicRouteHandler,
  PrivateRouteHandler,
} from "./handlers/RouteHandlers";
import { LOGIN_APP_URL ,DASHBOARD_APP_URL} from "../constants/appUrl";
import PublicTemplate from "./templates/public_route.template";
import Template from "./templates/private_route.template";
import {
  LOGIN_APP_COMPONENT,
  DASHBOARD_APP_COMPONENT,
} from "../constants/componentUrls";

const AllRoutes: React.FC = () => {
  // const userLoggedIn = isLoggedIn();
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);

  // Function to toggle between light and dark theme
  const toggleTheme = async () => {
    document.documentElement.setAttribute("data-bs-theme", currentTheme);
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    if (themeColorMeta) {
      themeColorMeta.setAttribute("content", currentTheme === "dark" ? "#ffffff" : "#282828");
    }
  };

  useEffect(() => {
    toggleTheme();
    // eslint-disable-next-line
  }, []);
  return (
    <Suspense fallback={<Loader />}>
      <Routes>

        <Route element={<PublicRouteHandler />}>
          <Route
            path={LOGIN_APP_URL}
            element={
              <PublicTemplate>
                <LOGIN_APP_COMPONENT />
              </PublicTemplate>
            }
          />
          {publicRoutes.map((route, index) => (
            <Route
              key={index}
              path={route.path}
              element={
                <PublicTemplate>
                  <route.component />
                </PublicTemplate>
              }
            />
          ))}
        </Route>
        <Route element={<PrivateRouteHandler />}>
          <Route
            path={DASHBOARD_APP_URL}
            element={
              <Template>
                <DASHBOARD_APP_COMPONENT />
              </Template>
            }
          />
          {privateRoutes.map((route, index) => (
            <Route
              key={index}
              path={route.path}
              element={
                <Template rolePermission={route.roles}>
                  <route.component />
                </Template>
              }
            />
          ))}
          </Route>

        {/** <Route path="*" element={<LOGIN_APP_COMPONENT />} />  TODO - NEED TO ADD PROPER NOT FOUND ROUTE */}
      </Routes>
    </Suspense>
  );
};

export default AllRoutes;
