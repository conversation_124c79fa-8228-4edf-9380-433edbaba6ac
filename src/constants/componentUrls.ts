import React from "react";

export const LOGIN_APP_COMPONENT = React.lazy(() => import("../pages/login"));
export const FORGET_PASSWORD_APP_COMPONENT = React.lazy(
  () => import("../pages/forgot-password")
);
export const REGISTER_APP_COMPONENT = React.lazy(() => import("../pages/register"));
export const DASHBOARD_APP_COMPONENT = React.lazy(
  () => import("../pages/manage-dashboard")
);
export const MANAGE_PROFILE_APP_COMPONENT = React.lazy(
  () => import("../pages/manage_profile")
);

export const MANAGE_RESEARCH_APP_COMPONENT = React.lazy(
  () => import("../pages/manage-research")
);
export const MANAGE_PATIENT_APP_COMPONENT = React.lazy(
  () => import("../pages/manage-patient")
);
export const MANAGE_PATIENT_CONSENT_APP_COMPONENT = React.lazy(
  () => import("../pages/manage-patient-consent")
);
export const MANAGE_VIDEO_APP_COMPONENT = React.lazy(
  () => import("../pages/manage-videos")
);
export const MANAGE_DECLARATION_APP_COMPONENT = React.lazy(
  () => import("../pages/manage-declarations")
);
export const MANAGE_CONSENT_FORMS_APP_COMPONENT= React.lazy(
  () => import("../pages/manage-consent-forms")
);
export const ADD_CONSENT_FORMS_APP_COMPONENT= React.lazy(
  () => import("../pages/add-consent-forms")
);
export const EDIT_CONSENT_FORMS_APP_COMPONENT= React.lazy(
  () => import("../pages/edit-consent-forms")
);




