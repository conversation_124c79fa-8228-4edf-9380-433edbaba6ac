export interface IFetchVideoDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
  perPage: number;
}

export interface IVideoRecord {
  id: string;
  title: string;
  description: string;
  file_path: string;
  mime_type : string;
  created_at: string;
}

export interface IFetchVideoDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
 // status: string;
  perPage: number;
}
export interface IVideoViewProps {
  selectedData: IVideoRecord;
  onDelete: () => void;
  onEdit: () => void;
  onClose: () => void;
}
export interface ISVideoFormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required: boolean;
  favImage?: any;
  image?: string;
  maxLength?: number;
  minLength?: number;
  accept?: string,
  maxSize?: number; 
  validationRules: {
    type: string;
    maxLength?: number;
    minLength?: number;
    required: boolean;
    accept?: string;
    maxSize?: number; 
  };
  passwordTooltipShow?: boolean;
  colProps?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}
