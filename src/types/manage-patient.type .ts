export interface FetchPatientDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
  perPage: number;
}

export interface IPatientRecord {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  active_status: boolean;
  created_at: string;
}
export interface IManagePatientState {
  records: IPatientRecord[];
  perPage: number;
  totalRecords: number;
  page: number;
  totalPages: number;
  sort: string;
  sortColumn: string;
  searchText: string;
  status: string;
}

export interface PatientListTableProps {
  fetchAllPatientDetails: (params: FetchPatientDetailsParams) => void;
}
export interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  active_status: boolean;
  created_at: string;
}
export interface IFetchPatientDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
  status: string;
  perPage: number;
}
export interface IPatientViewProps {
  selectedData: IPatientRecord;
  //onDelete: () => void;
  //onEdit: () => void;
  onClose: () => void;
}
export interface ISPatientFormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required: boolean;
  favImage?: any;
  image?: string;
  maxLength?: number;
  minLength?: number;
  validationRules: {
    type: string;
    maxLength?: number;
    minLength?: number;
    required: boolean;
  };
  passwordTooltipShow?: boolean;
  colProps?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}
