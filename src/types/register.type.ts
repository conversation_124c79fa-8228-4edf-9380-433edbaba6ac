import { NavigateFunction } from "react-router-dom";
import { AppDispatch } from "../redux/store";
import { InputField } from "./formInput.type";

export interface RegisterState {
  [key: string]: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface RegisterErrorState {
  [key: string]: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface RegisterField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required: boolean;
  image?: string;
  maxLength?: number;
  minLength?: number;
  validationRules: {
    type: string;
    maxLength?: number;
    minLength?: number;
    required: boolean;
  };
  passwordTooltipShow?: boolean;
}

export interface RegisterSubmitParams {
  registerState: RegisterState;
  dispatch: AppDispatch;
  navigate: NavigateFunction;
  setErrRegisterState: React.Dispatch<React.SetStateAction<RegisterErrorState>>;
  fields: InputField[];
}
