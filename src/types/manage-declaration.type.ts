export interface FetchDeclarationDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
  perPage: number;
}

export interface IDeclarationRecord {
  id: string;
  title: string;
  statement: string;
  response_type: string;
  created_at: string;
}

export interface IFetchDeclarationtDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
 // status: string;
  perPage: number;
}
export interface IDeclarationViewProps {
  selectedData: IDeclarationRecord;
  onDelete: () => void;
  onEdit: () => void;
  onClose: () => void;
}
export interface ISDeclarationFormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required: boolean;
  favImage?: any;
  image?: string;
  maxLength?: number;
  minLength?: number;
  validationRules: {
    type: string;
    maxLength?: number;
    minLength?: number;
    required: boolean;
  };
  options?: Array<{ value: string; label: string }>;
  passwordTooltipShow?: boolean;
  colProps?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}
