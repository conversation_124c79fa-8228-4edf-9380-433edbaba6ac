export interface IFetchResearchDetailsParams {
  currentPage: number;
  sort: string;
  sortColumn: string;
  searchText: string;
  perPage: number;
  status: string;
}

export interface ResearchListTableProps {
  fetchAllResearchDetails: (params: IFetchResearchDetailsParams) => void;
}

export interface IResearchViewProps {
  selectedData: IResearchRecord;
  onDelete: () => void;
  onEdit: () => void;
  onClose: () => void;
}

interface ResearchDocument {
  id: number;
  file_path: string;
  file_type: string;
  uploaded_by: number;
  user_name: string;
  created_at: string;
  updated_at: string;
}

export interface IResearchRecord {
  id: string;
  title: string;
  category_id:string;
  document:  ResearchDocument | null;
  category_name:string;
  is_published_flag:boolean;
  created_at: string;
  is_published:string
}

export interface ISResearchFormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required: boolean;
  favImage?: any;
  image?: string;
  maxLength?: number;
  minLength?: number;
  accept?: string,
  maxSize?: number; 
  validationRules: {
    type: string;
    maxLength?: number;
    minLength?: number;
    required: boolean;
    maxSize?: number; 
  };
  options?: Array<{ value: string; label: string }>;
  passwordTooltipShow?: boolean;
  colProps?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}
