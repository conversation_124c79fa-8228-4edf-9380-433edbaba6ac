import { AppDispatch } from "../../redux/store";
import {
  IFetchConsentFormDetailsParams
} from "../../types/manage-econsent.type";
import { SuccessMessageInterface } from "../../types/redux.type";
import { NavigateFunction } from "react-router-dom";
import {
    getEconsentDropDowndData,
    createAdminConsentForm,
    updateAdminConsentForm,
    getAllConsentFormsList,
    deleteEconsentForm,
    updateAdminEconsentFormStatus,
    setLoader,
    closeModal,
  } from "../../redux/action";
import { MANAGE_CONSENT_FORMS_APP_URL } from "../../constants/appUrl";
import {
  setPage,
  setPerPage,
  setSearchText,
  setSortColumn,
} from "../../redux/slices/manage-econsent";

export const handleFetchEconsentDropDowndData = async (
  dispatch: AppDispatch
) => {
  try {
    await dispatch(setLoader(true));

    const response = await dispatch(getEconsentDropDowndData() as any);
    return response; // <- return categories list here
  } catch (error) {
    console.error("Error fetching Research details:", error);
    return []; // <- return empty on error
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleFetchAllConsentForms = (
  dispatch: AppDispatch,
  params: IFetchConsentFormDetailsParams,
  navigate: NavigateFunction,
  location: any
) => {
  updateQueryParams(navigate, params, location); // Update query params
  fetchAllConsentForms(dispatch, params); // Fetch data
};
export const updateQueryParams = (
  navigate: NavigateFunction,
  params: IFetchConsentFormDetailsParams,
  location: Location
) => {
  navigate(
    {
      pathname: MANAGE_CONSENT_FORMS_APP_URL,
      search: `?currentPage=${String(params.currentPage)}&sort=${
        params.sort
      }&sortColumn=${params.sortColumn}&searchText=${
        params.searchText
      }&status=${params.status}&perPage=${String(params.perPage)}`,
    },
    { replace: false }
  );
};

export const fetchAllConsentForms = async (
  dispatch: AppDispatch,
  params: IFetchConsentFormDetailsParams
): Promise<void> => {
  try {
    await dispatch(setLoader(true));
    await dispatch(setPage(params.currentPage));
    await dispatch(setPerPage(params.perPage));
    await dispatch(setSortColumn(params.sortColumn));
    await dispatch(setSearchText(params.searchText));
    await dispatch(
      getAllConsentFormsList({
        page: params.currentPage,
        per_page: params.perPage,
        sort: params.sort,
        sort_column: params.sortColumn,
        search_text: params.searchText,
        status: params.status,
      })
    );
  } catch (error) {
    console.error("Error fetching staff details:", error);
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleAddConsentForm = async (
  dispatch: AppDispatch,
  data,
  navigate: NavigateFunction,
  location: any,
  getData :any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(
      createAdminConsentForm(data, async (response: SuccessMessageInterface) => {
        if (response.success) {
         
        }
      })
    );
    await handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: 1,
        sort: "desc",
        sortColumn: "created_at",
        searchText: "",
        status: "all",
        perPage:  getData?.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error creating user:", err);
    return { general: "Failed to create user" };
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleUpdateConsentForm = async (
  dispatch: AppDispatch,
  data,
  econsentId: string,
  navigate: NavigateFunction,
  location: any,
  getData :any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(
     
      updateAdminConsentForm(
              data,
              econsentId,
              async (response: SuccessMessageInterface) => {
                if (response.success) {
                 
                }
              }
      )
    );
    await handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: 1,
        sort: "desc",
        sortColumn: "created_at",
        searchText: "",
        status: "all",
        perPage: getData?.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error creating user:", err);
    return { general: "Failed to create user" };
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleEconsentStatusChange = async (
  dispatch: AppDispatch,
  econsentId: string,
  newStatus: boolean,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(closeModal());
    await dispatch(updateAdminEconsentFormStatus({ active_status: newStatus }, econsentId));
    await handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: getData.currentPage,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error updating user status:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleEconsentDeletion = async (
  dispatch: AppDispatch,
  econsentId: string,
  getData: any,
  navigate: NavigateFunction,
  location: any,
  records: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(closeModal());
    await dispatch(
      deleteEconsentForm({
        consent_id: econsentId,
      })
    );
    await handleFetchAllConsentForms(
      dispatch,
      {
        currentPage: records && records.length > 1  ? getData.currentPage : getData.currentPage - 1,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error deleting research:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

