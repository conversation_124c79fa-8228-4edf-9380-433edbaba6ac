import { AppDispatch } from "../../redux/store";

import { NavigateFunction } from "react-router-dom";
import {
    setLoader,
    getAdminDashboardData,
    
  } from "../../redux/action";

export const handleFetchAdminDashboardData = async (
  dispatch: AppDispatch,
   navigate: NavigateFunction,
  location: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(getAdminDashboardData());
   
  } catch (err) {
    console.error("Error fetching dashboard data:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

