import React from "react";
import {  useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import DashboardData from "../../components/features/admin-dashboard/DashboardData";
import ConsentListing from "../../components/features/patient-dashboard/ConsentListing";
import ThemeImage from "../../components/common/icons/ThemeImage";

const DashboardPage: React.FC = () => {

  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);
  const userRole = useAppSelector((state: RootState) => state.auth.user?.role); // Adjust path if needed
  //console.log('userRole',userRole?.name);


  return (
    <div className="main">
     
      <div className="row row-gap-3">
        <div className="col-lg-12">
          <div
            className="card main-head py-3 px-3 rounded-3"
            style={{ backgroundColor: "var(--bs-body-bg)" }}
          >
            <h3 className="m-0">
              {/* <FontAwesomeIcon icon={faTruck} className="me-2" /> */}
              <ThemeImage
                imageName="manageDashboardImage"
                alt="manageDashboardImage"
                className={
                  currentTheme === "dark"
                    ? "dark-icon-image me-2"
                    : "light-icon-image me-2"
                }
              />
              Dashboard
            </h3>
          </div>
        </div>
        {/* <ConsentListing /> */}
         {/* Conditional rendering based on role */}
         {userRole?.name === "admin" ? <DashboardData /> : <ConsentListing />}
      </div>
    </div>
  );
};

export default DashboardPage;