import { AppDispatch } from "../../redux/store";
import {
  ISDeclarationFormField,
  IFetchDeclarationtDetailsParams
} from "../../types/manage-declaration.type";
import { validateForm } from "../../utils/validation/validationUtils";
import { SuccessMessageInterface } from "../../types/redux.type";
import { NavigateFunction } from "react-router-dom";
import {
    createDeclaration,
    getAllDeclarationtList,
    deleteDeclaration,
    updateDeclarationDetails,
    setLoader,
    closeModal,
  } from "../../redux/action";
import { MANAGE_DECLARATION_APP_URL } from "../../constants/appUrl";
import {
  setPage,
  setPerPage,
  setSearchText,
  setSortColumn,
} from "../../redux/slices/manage-declarations";

export const handleFetchAllDeclarationDetails = (
  dispatch: AppDispatch,
  params: IFetchDeclarationtDetailsParams,
  navigate: NavigateFunction,
  location: any
) => {
  updateQueryParams(navigate, params, location); // Update query params
  fetchAllDeclarationDetails(dispatch, params); // Fetch data
};
export const updateQueryParams = (
  navigate: NavigateFunction,
  params: IFetchDeclarationtDetailsParams,
  location: Location
) => {
  navigate(
    {
      pathname: MANAGE_DECLARATION_APP_URL,
      search: `?currentPage=${String(params.currentPage)}&sort=${
        params.sort
      }&sortColumn=${params.sortColumn}&searchText=${
        params.searchText
      }&perPage=${String(params.perPage)}`,
    },
    { replace: false }
  );
};

export const fetchAllDeclarationDetails = async (
  dispatch: AppDispatch,
  params: IFetchDeclarationtDetailsParams
): Promise<void> => {
  try {
    await dispatch(setLoader(true));
    await dispatch(setPage(params.currentPage));
    await dispatch(setPerPage(params.perPage));
    await dispatch(setSortColumn(params.sortColumn));
    await dispatch(setSearchText(params.searchText));
    await dispatch(
      getAllDeclarationtList({
        page: params.currentPage,
        per_page: params.perPage,
        sort: params.sort,
        sort_column: params.sortColumn,
        search_text: params.searchText,
       // status: params.status,
      })
    );
  } catch (error) {
    console.error("Error fetching staff details:", error);
  } finally {
    await dispatch(setLoader(false));
  }
};


export const handleDeclarationCreation = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  userFormFields: ISDeclarationFormField[],
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, userFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }

  try {
    await dispatch(setLoader(true));
    await dispatch(
      createDeclaration(formState, async (response: SuccessMessageInterface) => {
        if (response.success) {
          dispatch(closeModal());
        }
      })
    );
    await handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: 1,
        sort: "desc",
        sortColumn: "created_at",
        searchText: "",
       // status: "all",
        perPage: getData?.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error creating user:", err);
    return { general: "Failed to create user" };
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleDeclarationDeletion = async (
  dispatch: AppDispatch,
  declarationId: string,
  getData: any,
  navigate: NavigateFunction,
  location: any,
  records: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(closeModal());
    await dispatch(
      deleteDeclaration({
        declaration_id: declarationId,
      })
    );
    await handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: records && records.length > 1  ? getData.currentPage : getData.currentPage - 1,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        //status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error deleting user:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleDeclarationUpdate = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  userFormFields: ISDeclarationFormField[],
  videoId: string,
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, userFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }
 
   try {
    await dispatch(setLoader(true));
    
    await dispatch(
      updateDeclarationDetails(
        {
          title: formState.title,
          statement: formState.statement,
          response_type: formState.response_type,
        },
        videoId,
        async (response: SuccessMessageInterface) => {
          if (response.success) {
            dispatch(closeModal());
          }
        }
      )
    );
    await handleFetchAllDeclarationDetails(
      dispatch,
      {
        currentPage: getData.currentPage,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
       // status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error updating user:", err);
    return { general: "Failed to update user" };
  } finally {
    await dispatch(setLoader(false));
  }
};



