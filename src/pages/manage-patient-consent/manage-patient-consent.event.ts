import { AppDispatch } from "../../redux/store";
import {
 IFetchPatientConsentDetailsParams
} from "../../types/manage-patient-consent.type";
//import { validateForm } from "../../utils/validation/validationUtils";
import { SuccessMessageInterface } from "../../types/redux.type";
import { NavigateFunction } from "react-router-dom";
import {
    getAdminPatientConsentList,
    setLoader,
    closeModal,
  } from "../../redux/action";
import { MANAGE_PATIENT_CONSENT_APP_URL } from "../../constants/appUrl";
import {
  setPage,
  setPerPage,
  setSearchText,
  setSortColumn,
} from "../../redux/slices/manage-patient-consent";

export const handleFetchAllPatientConsentDetails = (
  dispatch: AppDispatch,
  params: IFetchPatientConsentDetailsParams,
  navigate: NavigateFunction,
  location: any
) => {
  updateQueryParams(navigate, params, location); // Update query params
  fetchAllPatientConsentDetails(dispatch, params); // Fetch data
};
export const updateQueryParams = (
  navigate: NavigateFunction,
  params: IFetchPatientConsentDetailsParams,
  location: Location
) => {
  navigate(
    {
      pathname: MANAGE_PATIENT_CONSENT_APP_URL,
      search: `?currentPage=${String(params.currentPage)}&sort=${
        params.sort
      }&sortColumn=${params.sortColumn}&searchText=${
        params.searchText
      }&status=${params.status}&perPage=${String(params.perPage)}`,
    },
    { replace: false }
  );
};

export const fetchAllPatientConsentDetails = async (
  dispatch: AppDispatch,
  params: IFetchPatientConsentDetailsParams
): Promise<void> => {
  try {
    await dispatch(setLoader(true));
    await dispatch(setPage(params.currentPage));
    await dispatch(setPerPage(params.perPage));
    await dispatch(setSortColumn(params.sortColumn));
    await dispatch(setSearchText(params.searchText));
    await dispatch(
      getAdminPatientConsentList({
        page: params.currentPage,
        per_page: params.perPage,
        sort: params.sort,
        sort_column: params.sortColumn,
        search_text: params.searchText,
        status: params.status,
      })
    );
  } catch (error) {
    console.error("Error fetching patient consent details:", error);
  } finally {
    await dispatch(setLoader(false));
  }
};

