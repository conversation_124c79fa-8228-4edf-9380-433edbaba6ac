import React from "react";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import VideosList from "../../components/features/manage-videos/video-list.component";
import ThemeImage from "../../components/common/icons/ThemeImage";

const ManageVideos: React.FC = () => {
  const currentTheme = useAppSelector((state: RootState) => state.theme.theme);

  return (
    <div className="main">
      <div className="row row-gap-3">
        <div className="col-lg-12">
          <div
            className="card main-head  py-3 px-3 rounded-3"
            style={{ backgroundColor: "var(--bs-body-bg)" }}
          >
            <h3 className="m-0">
              {" "}
             <ThemeImage
                imageName="manageVideosImage"
                alt="manageVideosImage"
                className={
                  currentTheme === "dark"
                    ? "dark-icon-image me-2"
                    : "light-icon-image me-2"
                }
              />
              Manage Videos
            </h3>
          </div>
        </div>
        <VideosList />
      </div>
    </div>
  );
};

export default ManageVideos;