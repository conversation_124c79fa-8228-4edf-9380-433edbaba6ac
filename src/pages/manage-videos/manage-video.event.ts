import { AppDispatch } from "../../redux/store";
import {
  ISVideoFormField,
  IFetchVideoDetailsParams,
} from "../../types/manage-video.type";
import { validateForm } from "../../utils/validation/validationUtils";
import { SuccessMessageInterface } from "../../types/redux.type";
import { NavigateFunction } from "react-router-dom";
import {
    createVideo,
    updateVideo,
    getVideosList,
    deleteVideo,
    setLoader,
    closeModal,
  } from "../../redux/action";
import { MANAGE_VIDEO_APP_URL } from "../../constants/appUrl";
import {
  setPage,
  setPerPage,
  setSearchText,
  setSortColumn,
} from "../../redux/slices/manage-patient";
//import { Descriptions } from "antd";

export const handleFetchAllVideosDetails = (
  dispatch: AppDispatch,
  params: IFetchVideoDetailsParams,
  navigate: NavigateFunction,
  location: any
) => {
  updateQueryParams(navigate, params, location); // Update query params
  fetchAllVideosDetails(dispatch, params); // Fetch data
};
export const updateQueryParams = (
  navigate: NavigateFunction,
  params: IFetchVideoDetailsParams,
  location: Location
) => {
  navigate(
    {
      pathname: MANAGE_VIDEO_APP_URL,
      search: `?currentPage=${String(params.currentPage)}&sort=${
        params.sort
      }&sortColumn=${params.sortColumn}&searchText=${
        params.searchText
      }&perPage=${String(params.perPage)}`,
    },
    { replace: false }
  );
};

export const fetchAllVideosDetails = async (
  dispatch: AppDispatch,
  params: IFetchVideoDetailsParams
): Promise<void> => {
  try {
    await dispatch(setLoader(true));
    await dispatch(setPage(params.currentPage));
    await dispatch(setPerPage(params.perPage));
    await dispatch(setSortColumn(params.sortColumn));
    await dispatch(setSearchText(params.searchText));
    await dispatch(
      getVideosList({
        page: params.currentPage,
        per_page: params.perPage,
        sort: params.sort,
        sort_column: params.sortColumn,
        search_text: params.searchText,
        //status: params.status,
      })
    );
  } catch (error) {
    console.error("Error fetching staff details:", error);
  } finally {
    await dispatch(setLoader(false));
  }
};


export const handleVideoCreation = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  userFormFields: ISVideoFormField[],
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, userFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }
  
  //convert plain JavaScript object to file object
  const formData = new FormData();
  formData.append('title', formState.title);
  formData.append('description', formState.description);
  formData.append('video', formState.video);

  try {
    await dispatch(setLoader(true));
    await dispatch(
      createVideo(formData, async (response: SuccessMessageInterface) => {
        if (response.success) {
          dispatch(closeModal());
        }
      })
    );
    await handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: 1,
        sort: "desc",
        sortColumn: "created_at",
        searchText: "",
       // status: "all",
        perPage: getData?.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error creating user:", err);
    return { general: "Failed to create user" };
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleVideoDeletion = async (
  dispatch: AppDispatch,
  videoId: string,
  getData: any,
  navigate: NavigateFunction,
  location: any,
  records: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(closeModal());
    await dispatch(
      deleteVideo({
        video_id: videoId,
      })
    );
    await handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: records && records.length > 1  ? getData.currentPage : getData.currentPage - 1,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        //status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error deleting user:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleVideoUpdate = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  userFormFields: ISVideoFormField[],
  videoId: string,
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, userFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }
 
  //convert plain JavaScript object to file object
  const formData = new FormData();
  formData.append('title', formState.title);
  formData.append('description', formState.description);
  formData.append('video', formState.video);
  try {
    await dispatch(setLoader(true));
    
    await dispatch(
      updateVideo(
        // {
        //   title: formState.title,
        //   description: formState.description,
        //   video: formState.video,
        // },
        formData,
        videoId,
        async (response: SuccessMessageInterface) => {
          if (response.success) {
            dispatch(closeModal());
          }
        }
      )
    );
    await handleFetchAllVideosDetails(
      dispatch,
      {
        currentPage: getData.currentPage,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
       // status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error updating user:", err);
    return { general: "Failed to update user" };
  } finally {
    await dispatch(setLoader(false));
  }
};



