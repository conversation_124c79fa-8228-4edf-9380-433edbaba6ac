import { AppDispatch } from "../../redux/store";
import {
  IFetchResearchDetailsParams,
  ISResearchFormField,
} from "../../types/manage-research.type";
import { validateForm } from "../../utils/validation/validationUtils";
import { SuccessMessageInterface } from "../../types/redux.type";
import { NavigateFunction } from "react-router-dom";
import { MANAGE_RESEARCH_APP_URL } from "../../constants/appUrl";
import {
  setPage,
  setPerPage,
  setSearchText,
  setSortColumn,
} from "../../redux/slices/manage-research";
import {
  getResearchCategoriesList,
  addResearchCategory,
  getAllResearchList,
  createNewResearch,
  viewResearchDocument,
  updateResearchStatus,
  updateResearchData,
  deleteResearch,
  setLoader,
  closeModal,
} from "../../redux/action";

export const handleFetchAllResearchDetails = (
  dispatch: AppDispatch,
  params: IFetchResearchDetailsParams,
  navigate: NavigateFunction,
  location: any
) => {
  updateQueryParams(navigate, params, location); // Update query params
  fetchAllResearchDetails(dispatch, params); // Fetch data
};

export const updateQueryParams = (
  navigate: NavigateFunction,
  params: IFetchResearchDetailsParams,
  location: Location
) => {
  navigate(
    {
      pathname: MANAGE_RESEARCH_APP_URL,
      search: `?currentPage=${String(params.currentPage)}&sort=${
        params.sort
      }&sortColumn=${params.sortColumn}&searchText=${
        params.searchText
     // }&perPage=${String(params.perPage)}`,
      }&status=${params.status}&perPage=${String(params.perPage)}`,
    },
    { replace: false }
  );
};

export const fetchAllResearchDetails = async (
  dispatch: AppDispatch,
  params: IFetchResearchDetailsParams
): Promise<void> => {
  try {
    await dispatch(setLoader(true));
    await dispatch(setPage(params.currentPage));
    await dispatch(setPerPage(params.perPage));
    await dispatch(setSortColumn(params.sortColumn));
    await dispatch(setSearchText(params.searchText));
    await dispatch(
      getAllResearchList({
        page: params.currentPage,
        per_page: params.perPage,
        sort: params.sort,
        sort_column: params.sortColumn,
        search_text: params.searchText,
        status: params.status,
      })
    );
  } catch (error) {
    console.error("Error fetching Research details:", error);
  } finally {
    await dispatch(setLoader(false));
  }
};


export const handleFetchAllResearchCategories = async (
  dispatch: AppDispatch
): Promise<any[]> => {
  try {
    await dispatch(setLoader(true));

    const response = await dispatch(getResearchCategoriesList() as any);
    return response; // <- return categories list here
  } catch (error) {
    console.error("Error fetching Research details:", error);
    return []; // <- return empty on error
  } finally {
    await dispatch(setLoader(false));
  }
};


export const handleAddResearchCategories = async (
  dispatch: AppDispatch,
  data: { name: string }
) => {
  try {
    await dispatch(setLoader(true));

    const response = await dispatch(addResearchCategory(data));
    return response; // response is the `records` array
  } catch (error) {
    
    console.error("Error adding Research Category:", error);
    return [];
  } finally {
    
    await dispatch(setLoader(false));
  }
};


export const handleViewResearchDocument = async (
  dispatch: AppDispatch,
  document_id: string
) => {
  try {
    await dispatch(setLoader(true));
    const response = await dispatch(viewResearchDocument(document_id));
    return response;
  } catch (error) {
    console.error("Error viewing document:", error);
    return null;
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleResearchStatusChange = async (
  dispatch: AppDispatch,
  userId: string,
  newStatus: boolean,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(closeModal());
    await dispatch(updateResearchStatus({ active_status: newStatus }, userId));
    await handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: getData.currentPage,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error updating user status:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleResearchCreation = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  researchFormFields: ISResearchFormField[],
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, researchFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }

  //convert plain JavaScript object to file object
  const formData = new FormData();
  formData.append('title', formState.title);
  formData.append('category_id', formState.category_id);
  formData.append('document', formState.document);

  try {
    await dispatch(setLoader(true));
    await dispatch(
      createNewResearch(formData, async (response: SuccessMessageInterface) => {
        if (response.success) {
          dispatch(closeModal());
        }
      })
    );
    await handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: 1,
        sort: "desc",
        sortColumn: "created_at",
        searchText: "",
        status: "all",
        perPage: getData.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error creating user:", err);
    return { general: "Failed to create user" };
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handleResearchUpdate = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  researchFormFields: ISResearchFormField[],
  researchId: string,
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, researchFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }
  const formData = new FormData();
  formData.append('title', formState.title);
  formData.append('category_id', formState.category_id);
  formData.append('document', formState.document);

  try {
    await dispatch(setLoader(true));
    await dispatch(
      updateResearchData(
        
        formData,
        researchId,
        async (response: SuccessMessageInterface) => {
          if (response.success) {
            dispatch(closeModal());
          }
        }
      )
    );
    await handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: getData.currentPage,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error updating user:", err);
    return { general: "Failed to update user" };
  } finally {
    await dispatch(setLoader(false));
  }
};



export const handleResearchDeletion = async (
  dispatch: AppDispatch,
  researchId: string,
  getData: any,
  navigate: NavigateFunction,
  location: any,
  records: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(closeModal());
    await dispatch(
      deleteResearch({
        research_id: researchId,
      })
    );
    await handleFetchAllResearchDetails(
      dispatch,
      {
        currentPage: records && records.length > 1  ? getData.currentPage : getData.currentPage - 1,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error deleting research:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};
