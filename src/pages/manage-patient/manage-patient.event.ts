import { AppDispatch } from "../../redux/store";
import {
  ISPatientFormField,
  IFetchPatientDetailsParams
} from "../../types/manage-patient.type ";
import { validateForm } from "../../utils/validation/validationUtils";
import { SuccessMessageInterface } from "../../types/redux.type";
import { NavigateFunction } from "react-router-dom";
import {
    createAdminPatient,
    getAdminPatientList,
    updateAdminPatientStatus,
    setLoader,
    closeModal,
  } from "../../redux/action";
import { MANAGE_PATIENT_APP_URL } from "../../constants/appUrl";
import {
  setPage,
  setPerPage,
  setSearchText,
  setSortColumn,
} from "../../redux/slices/manage-patient";

export const handleFetchAllPatientDetails = (
  dispatch: AppDispatch,
  params: IFetchPatientDetailsParams,
  navigate: NavigateFunction,
  location: any
) => {
  updateQueryParams(navigate, params, location); // Update query params
  fetchAllPatientDetails(dispatch, params); // Fetch data
};
export const updateQueryParams = (
  navigate: NavigateFunction,
  params: IFetchPatientDetailsParams,
  location: Location
) => {
  navigate(
    {
      pathname: MANAGE_PATIENT_APP_URL,
      search: `?currentPage=${String(params.currentPage)}&sort=${
        params.sort
      }&sortColumn=${params.sortColumn}&searchText=${
        params.searchText
      }&status=${params.status}&perPage=${String(params.perPage)}`,
    },
    { replace: false }
  );
};

export const fetchAllPatientDetails = async (
  dispatch: AppDispatch,
  params: IFetchPatientDetailsParams
): Promise<void> => {
  try {
    await dispatch(setLoader(true));
    await dispatch(setPage(params.currentPage));
    await dispatch(setPerPage(params.perPage));
    await dispatch(setSortColumn(params.sortColumn));
    await dispatch(setSearchText(params.searchText));
    await dispatch(
      getAdminPatientList({
        page: params.currentPage,
        per_page: params.perPage,
        sort: params.sort,
        sort_column: params.sortColumn,
        search_text: params.searchText,
        status: params.status,
      })
    );
  } catch (error) {
    console.error("Error fetching staff details:", error);
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handlePatientCreation = async (
  dispatch: AppDispatch,
  formState: Record<string, string>,
  userFormFields: ISPatientFormField[],
  setErrorState: any,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  const validationErrors = validateForm(formState, userFormFields);

  if (Object.keys(validationErrors).length > 0) {
    setErrorState((prevErrors: any) => ({
      ...prevErrors,
      ...validationErrors,
    }));
    return;
  }

  try {
    await dispatch(setLoader(true));
    await dispatch(
      createAdminPatient(formState, async (response: SuccessMessageInterface) => {
        if (response.success) {
          dispatch(closeModal());
        }
      })
    );
    await handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: 1,
        sort: "desc",
        sortColumn: "created_at",
        searchText: "",
        status: "all",
        perPage: getData.perPage,
      },
      navigate,
      location
    );
    return {};
  } catch (err) {
    console.error("Error creating user:", err);
    return { general: "Failed to create user" };
  } finally {
    await dispatch(setLoader(false));
  }
};

export const handlePatientStatusChange = async (
  dispatch: AppDispatch,
  userId: string,
  newStatus: boolean,
  getData: any,
  navigate: NavigateFunction,
  location: any
) => {
  try {
    await dispatch(setLoader(true));
    await dispatch(updateAdminPatientStatus({ active_status: newStatus }, userId));
    await handleFetchAllPatientDetails(
      dispatch,
      {
        currentPage: getData.currentPage,
        sort: getData.sort,
        sortColumn: getData.sortColumn,
        searchText: getData.searchText,
        status: getData.status,
        perPage: getData.perPage,
      },
      navigate,
      location
    );
  } catch (err) {
    console.error("Error updating user status:", err);
  } finally {
    await dispatch(setLoader(false));
  }
};

