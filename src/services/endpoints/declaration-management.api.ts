

import { API } from "../api";
import { DECLARATION_LIST_API } from "../constants/api-constants";



const createNewDeclaration = (payload: any) => {
  return API.post(DECLARATION_LIST_API, payload);
};


const fetchAllDeclarationtList = (params: any) => {
  return API.get(DECLARATION_LIST_API, params);
  
};

const deleteDeclaration = (data: any) => {
  return API.delete(`${DECLARATION_LIST_API}/${data.declaration_id}`, {});
};

const updateDeclaration = (payload: any, declarationId: string) => {
  return API.put(`${DECLARATION_LIST_API}/${declarationId}`, payload);
};


// eslint-disable-next-line
export const declarationManagementServices = {
    createNewDeclaration,
    fetchAllDeclarationtList,
    deleteDeclaration,
    updateDeclaration
 
};
