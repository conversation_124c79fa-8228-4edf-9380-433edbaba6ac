

import { API } from "../api";
import { ECONSENT_DROPDOWN_DATA_API,ADMIN_ECONSENT_API } from "../constants/api-constants";


// Create a new admin patient
const createAdminConsentForm = (payload: any) => {
  return API.post(ADMIN_ECONSENT_API, payload);
};



const fetchAllEconsentDropDowndData = () => {
  return API.get(ECONSENT_DROPDOWN_DATA_API);
  
};

const fetchAllConsentFormsList = (params: any) => {
  return API.get(ADMIN_ECONSENT_API, params);
  
};


const updateEconsentFormStatus = (payload: any, econsentId: string) => {
  return API.patch(`${ADMIN_ECONSENT_API}/${econsentId}/publish`, payload);
};

const deleteEconsentForm = (data: any) => {
  return API.delete(`${ADMIN_ECONSENT_API}/${data.consent_id}`, {});
};

const updateConsentForm = (payload: any, econsentId: string) => {
  return API.put(`${ADMIN_ECONSENT_API}/${econsentId}`, payload);
};


// eslint-disable-next-line
export const econsentManagementServices = {
  createAdminConsentForm,
  fetchAllEconsentDropDowndData,
  fetchAllConsentFormsList,
  deleteEconsentForm,
  updateEconsentFormStatus,
  updateConsentForm
 
};
