

import { API } from "../api";
import { VIDEO_LIST_API} from "../constants/api-constants";


// Create a new admin patient
const uploadNewVideo = (payload: any) => {
  return API.post(`${VIDEO_LIST_API}/upload`, payload);
};



const fetchAllVideosList = (params: any) => {
  return API.get(VIDEO_LIST_API, params);
  
};
const deleteVideo = (data: any) => {
  return API.delete(`${VIDEO_LIST_API}/${data.video_id}`, {});
};

const updateVideo = (payload: any, videoId: string) => {
  return API.patch(`${VIDEO_LIST_API}/${videoId}`, payload);
};


// eslint-disable-next-line
export const videoManagementServices = {
  uploadNewVideo,
  updateVideo,
  fetchAllVideosList,
  deleteVideo
  
 
};
