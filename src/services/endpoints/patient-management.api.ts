

import { API } from "../api";
import { ADMIN_PATIENT_API,ADMIN_PATIENT_ECONSENT_API } from "../constants/api-constants";


// Create a new admin patient
const createAdminPatient = (payload: any) => {
  return API.post(ADMIN_PATIENT_API, payload);
};


const fetchAdminPatientList = (params: any) => {
  return API.get(ADMIN_PATIENT_API, params);
  
};

const updatePatientUserStatus = (payload: any, userId: string) => {
  return API.patch(`${ADMIN_PATIENT_API}/${userId}/change-status`, payload);
};

const fetchAdminPatientConsentList = (params: any) => {
  return API.get(ADMIN_PATIENT_ECONSENT_API, params);
  
};



// eslint-disable-next-line
export const patientManagementServices = {
  createAdminPatient,
  fetchAdminPatientList,
  updatePatientUserStatus,
  fetchAdminPatientConsentList
 
};
