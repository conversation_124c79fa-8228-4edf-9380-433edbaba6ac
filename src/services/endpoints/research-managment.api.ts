

import { API } from "../api";
import { RESEARCH_CATEGORIES_LIST_API,RESEARCH_LIST_API} from "../constants/api-constants";
import axios from "axios";

// Create a new admin patient

const fetchAllResearchCategoriesList = () => {
  return API.get(RESEARCH_CATEGORIES_LIST_API);
  
};

const fetchAllResearchList = (params: any) => {
  return API.get(RESEARCH_LIST_API, params);
  
};
const deleteResearch = (data: any) => {
  return API.delete(`${RESEARCH_LIST_API}/${data.research_id}`, {});
};

// const updateVideo = (payload: any, videoId: string) => {
//   return API.patch(`${VIDEO_LIST_API}/${videoId}`, payload);
// };

 const addNewResearchCategory = (payload: any) => {
     return API.post(`${RESEARCH_CATEGORIES_LIST_API}`, payload);
   };
  const uploadNewResearch = (payload: any) => {
    return API.post(`${RESEARCH_LIST_API}`, payload);
  };
  
const updateResearchStatus = (payload: any, researchId: string) => {
  return API.patch(`${RESEARCH_LIST_API}/${researchId}/publish`, payload);
};
const updateResearchdata = (payload: any, researchId: string) => {
  return API.put(`${RESEARCH_LIST_API}/${researchId}`, payload);
};

const viewResearchDocument = (documentId: string) => {
  return API.getBlob(`${RESEARCH_LIST_API}/${documentId}/document`);
};


// eslint-disable-next-line
export const researchManagementServices = {
  
  fetchAllResearchCategoriesList,
  fetchAllResearchList,
  addNewResearchCategory,
  uploadNewResearch,
  deleteResearch,
  updateResearchStatus,
  viewResearchDocument,
  updateResearchdata
  
 
};
